{"root": true, "ignorePatterns": ["**/*"], "overrides": [{"files": ["*.ts"], "settings": {"import/internal-regex": "^@kurt/"}, "extends": ["./.eslintrc.base.js", "eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking", "plugin:import/recommended", "plugin:import/typescript"], "plugins": ["@nx"], "rules": {"import/order": ["warn", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index", "object", "type"], "alphabetize": {"order": "asc", "caseInsensitive": true}, "newlines-between": "always"}], "import/no-unresolved": "off", "no-unused-expressions": "off", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": ["error", {"vars": "all", "args": "all", "ignoreRestSiblings": true, "argsIgnorePattern": "^_"}], "@typescript-eslint/explicit-module-boundary-types": "off", "arrow-body-style": "off", "@nx/enforce-module-boundaries": ["error", {"enforceBuildableLibDependency": true, "allow": [], "depConstraints": []}], "@typescript-eslint/consistent-type-definitions": "error", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-namespace": "off", "@typescript-eslint/dot-notation": "off", "@typescript-eslint/explicit-member-accessibility": ["error", {"accessibility": "no-public"}], "@typescript-eslint/member-ordering": ["error", {"default": ["signature", "public-static-field", "protected-static-field", "private-static-field", "public-decorated-field", "protected-decorated-field", "private-decorated-field", "public-instance-field", "protected-instance-field", "private-instance-field", "public-constructor", "protected-constructor", "private-constructor", "public-static-method", "protected-static-method", "private-static-method", "public-decorated-method", "protected-decorated-method", "private-decorated-method", "public-instance-method", "protected-instance-method", "private-instance-method"]}], "@typescript-eslint/naming-convention": ["error", {"selector": "default", "format": ["camelCase"], "leadingUnderscore": "allow", "trailingUnderscore": "allow"}, {"selector": "variable", "format": ["camelCase", "UPPER_CASE"], "leadingUnderscore": "allow", "trailingUnderscore": "allow"}, {"selector": "typeLike", "format": ["PascalCase"]}, {"selector": "enumMember", "format": ["PascalCase"]}, {"selector": "classProperty", "modifiers": ["static"], "prefix": ["ngAcceptInputType_"], "format": ["camelCase"]}, {"selector": "classProperty", "modifiers": ["public", "readonly"], "format": ["PascalCase"], "filter": {"regex": "Enum$", "match": true}}, {"selector": "objectLiteralProperty", "modifiers": ["requiresQuotes"], "format": null}], "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-empty-interface": "error", "@typescript-eslint/no-inferrable-types": ["error", {"ignoreParameters": true}], "@typescript-eslint/no-misused-new": "error", "@typescript-eslint/no-non-null-assertion": "error", "@typescript-eslint/no-shadow": ["error", {"hoist": "all"}], "@typescript-eslint/no-floating-promises": "off", "@typescript-eslint/no-misused-promises": ["error", {"checksVoidReturn": false}], "@typescript-eslint/no-unused-expressions": "error", "@typescript-eslint/prefer-function-type": "error", "@typescript-eslint/unified-signatures": "error", "@typescript-eslint/unbound-method": ["error", {"ignoreStatic": true}], "constructor-super": "error", "eqeqeq": ["error", "smart"], "guard-for-in": "error", "id-blacklist": "off", "id-match": "off", "import/no-deprecated": "warn", "no-bitwise": "error", "no-caller": "error", "no-console": "error", "no-debugger": "error", "no-empty": "off", "no-eval": "error", "no-fallthrough": "error", "no-new-wrappers": "error", "no-restricted-imports": ["error", "rxjs/Rx"], "no-throw-literal": "error", "no-undef-init": "error", "no-underscore-dangle": "off", "no-var": "error", "prefer-const": "error", "radix": "error"}}]}