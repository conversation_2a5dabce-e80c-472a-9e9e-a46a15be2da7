{"client": "Thunder Client", "collectionName": "kurt", "dateExported": "2022-05-11T12:40:30.551Z", "version": "1.1", "folders": [], "requests": [{"_id": "4be73522-933a-4b01-b90c-630f24088ac0", "colId": "8a0eba52-41c8-4c7a-8949-0db7e0037184", "containerId": "", "name": "Stop", "url": "localhost:3333/api/v2/{{gmeetId}}/stop-recording", "method": "GET", "sortNum": 10000, "created": "2022-05-11T11:18:00.253Z", "modified": "2022-05-11T12:22:38.639Z", "headers": [{"name": "Authorization", "value": "{{token}}"}], "params": [], "tests": []}, {"_id": "f5804010-2745-4198-8328-5c3f63fc7a2f", "colId": "8a0eba52-41c8-4c7a-8949-0db7e0037184", "containerId": "", "name": "Leave", "url": "localhost:3333/api/v2/{{gmeetId}}/leave", "method": "GET", "sortNum": 15000, "created": "2022-05-11T12:27:32.503Z", "modified": "2022-05-11T12:27:42.553Z", "headers": [{"name": "Authorization", "value": "{{token}}"}], "params": [], "tests": []}, {"_id": "65d81f5f-60cc-4005-9436-4be312cee746", "colId": "8a0eba52-41c8-4c7a-8949-0db7e0037184", "containerId": "", "name": "Language", "url": "localhost:3333/api/jzd-qgqd-rzw/language", "method": "PUT", "sortNum": 20000, "created": "2022-05-11T11:18:06.998Z", "modified": "2022-05-11T11:18:06.998Z", "headers": [{"name": "Authorization", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6ImVmMzAxNjFhOWMyZGI3ODA5ZjQ1MTNiYjRlZDA4NzNmNDczMmY3MjEiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W1X2iBKKx7FAv4z8dkhjB-JYrOMUGm6wtd6oth8e_82DchHWcir5sE6VJpscfpa5Wrijc852a876-TqwrXIfQ-gP15yeTDXXp0qpcnWDwAThkJSaW77hwoeeCStf6jUV28e9duvVTzJav4EihcszdlRMmf78Xaf_EOl0LjneJFNpiNG9HfX9l8IoHKwJcBfMl6WPTSGLkS2YFVKeJymBEqsQnTsBX6BSyraPQ92GXXNzM2af8jp3tlnLQDYGxBoW9UkH03ke0efgXqILlB139-88m_VTVX_EHw5HStNNI4XYmLPrapotedjheICIhH9-epBfynMpeQz_5d_KZrOxEg "}], "params": [], "body": {"type": "text", "raw": "de", "form": []}, "tests": []}, {"_id": "2c7e89ef-88ca-4136-af0b-776f8a147d13", "colId": "8a0eba52-41c8-4c7a-8949-0db7e0037184", "containerId": "", "name": "Start", "url": "localhost:3333/api/v2/{{gmeetId}}/start-recording?documentId=fgagergiu234&lang=de", "method": "GET", "sortNum": 30000, "created": "2022-05-11T11:18:12.556Z", "modified": "2022-05-11T12:15:40.440Z", "headers": [{"name": "Authorization", "value": "{{token}}"}], "params": [{"name": "documentId", "value": "fgagergiu234", "isPath": false}, {"name": "lang", "value": "de", "isPath": false}], "tests": []}], "settings": {"headers": [], "tests": [], "envId": "2f64a7af-0b99-4a9c-97d6-7f60f9bcf6d8"}}