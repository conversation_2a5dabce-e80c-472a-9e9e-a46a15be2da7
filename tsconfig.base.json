{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2019", "module": "esnext", "lib": ["es2017", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@kurt/bot/transcriber-script": ["packages/bot/transcriber-script/src/index.ts"], "@kurt/shared": ["packages/shared/src/index.ts"], "@kurt/transcriber": ["packages/transcriber/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}