# Kurt <PERSON>

## Useful commands

- Install dependencies: `npm ci`
- Build client-side script: `npx nx build bot-transcriber-script`
- Run bot in development mode: `npx nx serve bot`
- Run bot in production mode: `npx nx serve bot –configuration production`
- See the dependency graph of the packages: `npx nx dep-graph`

## Notes

- Before you run the bot for the first time you need to build the client-side script. This also needs to be done every time you make changes to the client-side code.
- When the bot runs in development mode it doesn’t write any data to the Firebase db. Instead the changes are logged to the console.
- The Chrome extension packages have not been maintained for quite some time.

## Structure

The project is split into 5 different packages located in the **packages** folder. There is also a VS Code workspace configuration file that creates top-level entries for each folder.

The [shared](./packages/shared/README.md) package contains some shared types and the definition for the logger service.

The [transcriber](./packages/transcriber/README.md) package contains the services that extract captions, audio, chat messages and the participants from the Google Meet page.

The [bot](./packages/bot/README.md) package contains all the server-side code for the bot. It also contains the [transcriber-script](./packages/bot/transcriber-script/README.md) sub-folder that contains initialization code for the client-side script.

The [plugin](./packages/plugin/README.md) package is a Chrome extension that can transcribe the meetings without the need for the bot.

The [chrome-extension](./packages/chrome-extension/README.md) package is a Chrome extension that can invite the bot to a meeting.

## Dependencies

- [express](https://expressjs.com/)
- [date-fns](https://date-fns.org/)
- [Puppeteer](https://pptr.dev/)
- [injection-js](https://github.com/mgechev/injection-js)
