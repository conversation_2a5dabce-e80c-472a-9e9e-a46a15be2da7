# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
/tmp
/out-tsc
/dist

# dependencies
/node_modules

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db

/debug/*.jpg
*.webm
*.mp3

.github/instructions/nx.instructions.md
.nx
.vscode
