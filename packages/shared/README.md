# shared

## Content

This library contains [types and constants](./src//lib/types.ts) that are shared between the other packages of the project and the definition of the [logger](./src/lib/logger.ts) service.

## Logger

This service is used to provide common formatting of the log messages. It expects a backend implementation that will actually log the formatted messages and an Id for each instance.
