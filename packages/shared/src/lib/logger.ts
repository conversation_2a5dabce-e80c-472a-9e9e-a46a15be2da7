import { Inject, Injectable, InjectionToken, Provider } from 'injection-js';

export const LOGGER_BACKEND = new InjectionToken<LoggerBackend>(
  'Logger Backend'
);

export interface LoggerBackend {
  log(this: void, message: string): void;
  error(this: void, message: string): void;
}

export const provideLoggerBackend = (
  loggerBackend: LoggerBackend
): Provider => {
  return {
    provide: LOGGER_BACKEND,
    useValue: loggerBackend,
  };
};

export const LOGGER_ID = new InjectionToken<string>('Logger Id');

export const provideLoggerId = (loggerId: string): Provider => {
  return {
    provide: LOGGER_ID,
    useValue: loggerId,
  };
};

@Injectable()
export class Logger {
  private readonly messagePrefix = `[KURT] [${this.gmeetId}]`;

  constructor(
    @Inject(LOGGER_BACKEND) private loggerBackend: LoggerBackend,
    @Inject(LOGGER_ID) private gmeetId: string
  ) {}

  error(error: Error): void {
    this.loggerBackend.error(`${this.messagePrefix} ${error.name}: ${
      error.message
    }
    ${error.stack ?? ''}`);
  }

  log(message: string): void {
    this.loggerBackend.log(`${this.messagePrefix} ${message}`);
  }
}
