import { InjectionToken, Provider } from 'injection-js';

export interface Transcript {
  person: string;
  image: string;
  text: string;
  startedAt: Date;
  endedAt: Date;
  language?: string;
}

export interface ExtensionEventBase {
  data: string;
  startedAt: Date;
}

export interface TranscriptionStartedEvent extends ExtensionEventBase {
  readonly type: 'TRANSCRIPTION_STARTED';
}

export interface TranscriptionStoppedEvent extends ExtensionEventBase {
  readonly type: 'TRANSCRIPTION_STOPPED';
}

export interface ParticipantJoinedEvent extends ExtensionEventBase {
  readonly type: 'PARTICIPANT_JOINED';
}

export interface ParticipantLeftEvent extends ExtensionEventBase {
  readonly type: 'PARTICIPANT_LEFT';
}

export type ExtensionEvent =
  | TranscriptionStartedEvent
  | TranscriptionStoppedEvent
  | ParticipantJoinedEvent
  | ParticipantLeftEvent;

export interface Participant {
  avatar: string;
  name: string;
  title?: string;
  subtitle?: string;
}

export interface TaskOrRecord {
  title: string;
  ['creator-name']: string;
  startedAt: Date;
}

export interface Meeting {
  id: string;
  date: Date;
  title: string;
  summary: string;
  transcribedBy?: string | null;
  'organizer-email'?: string | null;
  'organizer-name'?: string | null;
  'accessed-by'?: string[];
  lastRecording?: Date;
  botJoined?: Date;
  liveInfo?: MeetingLiveInfo;
}

export interface MeetingLiveInfo {
  isLive: boolean;
  status: string;
  recordingLanguage?: string;
  languageChanges?: Map<string, string>;
}

export interface ErrorInfo {
  message: string;
  stack?: string;
  name?: string;
  timestamp: Date;
  context?: string;
  type?: string;
}

export interface Screenshot {
  data: string;
  person: string;
  startedAt: Date;
  isSaved: boolean;
  errorInfo?: ErrorInfo;
}

export interface SpeakerInfo {
  name: string;
  isSpeaking: boolean;
  date: Date;
  isMuted: boolean;
}

export const RECORD_CREATED_EVENT_NAME = 'recordCreated';
export interface RecordCreatedEventDetail {
  title: string;
  'creator-name': string;
  language?: string;
}

export const TASK_CREATED_EVENT_NAME = 'taskCreated';
export interface TaskCreatedEventDetail {
  title: string;
  'creator-name': string;
  language?: string;
}

export const enum MeetingCollection {
  Transcripts = 'transcripts',
  Tasks = 'tasks',
  Events = 'events',
  Screenshots = 'screenshots',
  Messages = 'messages',
  Records = 'records',
  Speakers = 'speakers',
}

export const DATABASE_BACKEND = new InjectionToken<DatabaseBackend>(
  'Database Backend',
);

export const provideDatabaseBackend = (
  databaseBackend: DatabaseBackend,
): Provider => {
  return {
    provide: DATABASE_BACKEND,
    useValue: databaseBackend,
  };
};

export interface DatabaseBackend {
  initialize(this: void, meetingId: string): Promise<void>;
  getCurrentUserDisplayName(this: void): Promise<string>;
  getCurrentUserEmail(this: void): Promise<string>;
  getMeeting(this: void): Promise<Meeting | undefined>;
  createMeeting(this: void, meeting: Meeting): Promise<Meeting>;
  updateMeeting(this: void, updates: Partial<Meeting>): Promise<Meeting>;
  addSpeakerInfo(this: void, updates: SpeakerInfo): Promise<void>;
  updateMeetingLiveInfo(
    this: void,
    updates: Partial<MeetingLiveInfo>,
  ): Promise<void>;
  createDocument(
    this: void,
    collection: MeetingCollection.Transcripts | MeetingCollection.Messages,
    id: string,
    data: Transcript,
  ): Promise<void>;
  createDocument(
    this: void,
    collection: MeetingCollection.Tasks | MeetingCollection.Records,
    id: string,
    data: TaskOrRecord,
  ): Promise<void>;
  createDocument(
    this: void,
    collection: MeetingCollection.Events,
    id: string,
    data: ExtensionEvent,
  ): Promise<void>;
  createDocument(
    this: void,
    collection: MeetingCollection.Screenshots,
    id: string,
    data: Screenshot,
  ): Promise<void>;

  updateDocument(
    this: void,
    collection: MeetingCollection.Transcripts,
    id: string,
    data: Partial<Transcript>,
  ): Promise<void>;

  saveAudioRecording(
    this: void,
    details: {
      data: string[];
      startedAt: Date;
      endedAt: Date;
    },
  ): Promise<void>;

  saveErrorScreenshot(
    this: void,
    data: string,
    meetingId: string | undefined,
  ): Promise<void>;
}
