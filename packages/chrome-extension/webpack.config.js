const modifyConfig = /** @arg config {import('webpack').Configuration} @return {import('webpack').Configuration} */(config) => {
  return {
    ...config,
    entry: {
      // content: ['./packages/chrome-extension/src/app/content.ts'],
      background: ['./packages/chrome-extension/src/app/background.ts'],
      popup: ['./packages/chrome-extension/src/app/popup.ts'],
    },
    output: {
      path: config.output.path,
      filename: '[name].js',
    },
    target: undefined,
    experiments: {
      topLevelAwait: true
    }
  };
};

module.exports = modifyConfig
