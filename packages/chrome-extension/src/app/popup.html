<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>KURT - Google Meet Transcripts Options</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="stylesheet" href="firebaseui.css" />
  </head>
  <body style="width: 20rem;">
    <div id="login"></div>
    <div id="logged-in" style="display: flex; flex-direction: column">
      <div>Logged in as <span id="user-display-name"></span></div>
      <button id="logout" type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-button--colored" style="margin: 2rem auto">Logout</button>
    </div>
    <div id="actions" style="flex-direction: column; row-gap: 0.5rem;">
        <button id="start-recording" class="mdl-button mdl-js-button">
          Start recording
        </button>

        <button id="leave-meeting" class="mdl-button mdl-js-button">
          Stop recording
        </button>

        <!-- <div id="language-picker" style="display: flex; column-gap: 0.5rem;">
          <select id="language">
            <option value="en">English</option>
            <option value="de">German</option>
            <option value="fr">French</option>
            <option value="pr">Portuguese</option>
            <option value="es">Spanish</option>
          </select>
        <button id="change-language" class="mdl-button mdl-js-button" style="flex-grow: 1;">Change language</button>
      </div> -->
    </div>
    <script type="module" src="./popup.js"></script>
  </body>
</html>

