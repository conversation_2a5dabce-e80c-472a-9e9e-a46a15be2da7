import * as firebaseui from 'firebaseui';
import { auth } from './firebase-app';
import { UserImpl } from '@firebase/auth/internal';
import { EmailAuthProvider, User } from 'firebase/auth';
import { environment } from '../environments/environment';

export const enum MeetingStatus {
  None = 'N/A',
  Joined = 'Joined',
  Recording = 'Recording',
}

const ui = new firebaseui.auth.AuthUI(auth);
// eslint-disable-next-line @typescript-eslint/no-non-null-assertion
const loginDiv = document.querySelector<HTMLDivElement>('#login')!;
const actionsDiv = document.querySelector<HTMLDivElement>('#actions');
const loggedInDiv = document.querySelector<HTMLDivElement>('#logged-in');
const userDisplayNameSpan =
  document.querySelector<HTMLSpanElement>('#user-display-name');
const logoutButton = document.querySelector<HTMLButtonElement>('#logout');
// const languagePickerDiv =
//   document.querySelector<HTMLDivElement>('#language-picker');
const startRecordingButton =
  document.querySelector<HTMLButtonElement>('#start-recording');
const leaveMeetingButton =
  document.querySelector<HTMLButtonElement>('#leave-meeting');
// const changeLanguageButton =
//   document.querySelector<HTMLButtonElement>('#change-language');
const [activeTab] = await chrome.tabs.query({
  active: true,
  currentWindow: true,
});
const gmeetId = new URL(activeTab.url).pathname.replace('/', '');

logoutButton.addEventListener('click', () => {
  chrome.storage.local.set({ user: null });
  auth.signOut();
  showLoginForm();
});

startRecordingButton.addEventListener('click', () => {
  chrome.storage.local.get('user', async ({ user }) => {
    const idToken = await UserImpl._fromJSON(auth as any, user).getIdToken();

    try {
      const response = await fetch(
        `${environment.botUrl}/${gmeetId}/start-recording`,
        {
          headers: {
            Authorization: idToken,
          },
        }
      );

      if (!response.ok) {
        throw new Error(await response.text())
      }

      startRecordingButton.style.display = 'none'
      leaveMeetingButton.style.display = 'block'
    } catch (e) {
      console.log(e);
    }
  });
});

leaveMeetingButton.addEventListener('click', () => {
  chrome.storage.local.get('user', async ({ user }) => {
    const idToken = await UserImpl._fromJSON(auth as any, user).getIdToken();

    try {
      const response = await fetch(
        `${environment.botUrl}/${gmeetId}/leave`,
        {
          headers: {
            Authorization: idToken,
          },
        }
      );

      if (!response.ok) {
        throw new Error(await response.text())
      }

      startRecordingButton.style.display = 'block'
      leaveMeetingButton.style.display = 'none'
    } catch (e) {
      console.log(e);
    }
  });
});

if (auth.currentUser) {
  loginDiv.textContent = `Logged in as ${auth.currentUser.displayName}`;
} else {
  chrome.storage.local.get('user', ({ user }) => {
    if (user) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      auth.updateCurrentUser(UserImpl._fromJSON(auth as any, user));
      showLoggedIn(user);
    } else {
      showLoginForm();
    }
  });
}

async function showLoggedIn(user: User) {
  userDisplayNameSpan.innerHTML = user.displayName;
  loggedInDiv.style.display = 'flex';
  actionsDiv.style.display = 'flex';

  chrome.storage.local.get('user', async ({ user }) => {
    const idToken = await UserImpl._fromJSON(auth as any, user).getIdToken();

    const statusResponse = await fetch(
      `${environment.botUrl}/${gmeetId}/status`,
      {
        headers: {
          Authorization: idToken,
        },
      }
    ).then((r) => r.json());

    const { status } = statusResponse as {
      status: MeetingStatus;
      language?: string;
    };

    if (status === MeetingStatus.None || status === MeetingStatus.Joined) {
      // languagePickerDiv.style.display = 'none';
      leaveMeetingButton.style.display = 'none';
      startRecordingButton.style.display = 'block'
      // changeLanguageButton.style.display = 'none';
    } else {
      leaveMeetingButton.style.display = 'block';
      startRecordingButton.style.display = 'none'
    }
  });
}

function showLoginForm() {
  loggedInDiv.style.display = 'none';
  actionsDiv.style.display = 'none';

  ui.start(loginDiv, {
    signInOptions: [EmailAuthProvider.PROVIDER_ID],
    callbacks: {
      signInSuccessWithAuthResult: function (authResult) {
        const user = JSON.parse(JSON.stringify(authResult.user));
        // User successfully signed in.
        // Return type determines whether we continue the redirect automatically
        // or whether we leave that to developer to handle.
        chrome.storage.local.set({
          user,
        });

        showLoggedIn(user);

        return false;
      },
    },
  });
}
