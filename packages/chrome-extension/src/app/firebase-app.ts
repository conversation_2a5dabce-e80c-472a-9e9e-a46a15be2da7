import { getAuth } from "firebase/auth";
import { UserImpl } from '@firebase/auth/internal';
import { initializeApp } from 'firebase/app';
import 'firebase/auth';

export const app = initializeApp({
  apiKey: "AIzaSyBrPlfcbDdnEDR3Nx9vsTZTmAF2yS9oTMw",
  authDomain: "d-nbi-kurt.firebaseapp.com",
  projectId: "d-nbi-kurt",
  storageBucket: "d-nbi-kurt.appspot.com",
  messagingSenderId: "546060646634",
  appId: "1:546060646634:web:94dc74d117f00a29a1ff93",
  measurementId: "G-TYKDGGNN4X"
});
export const auth = getAuth(app);

chrome.storage.local.get('user', ({ user }) => {
  if (user) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    auth.updateCurrentUser(UserImpl._fromJSON(auth as any, user));

    auth.onIdTokenChanged((e) => e.getIdToken())
  }
});
