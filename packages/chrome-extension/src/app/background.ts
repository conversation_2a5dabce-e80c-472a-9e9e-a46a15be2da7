import { logoObjectUrl } from './logo';

const eventHandlers: Partial<
  Record<
    ExtensionEvent['eventType'],
    (event: ExtensionEvent, sender: chrome.runtime.MessageSender) => void
  >
> = {
  NOTIFICATION: (event) => {
    const { message, title } = event as NotificationEvent;
    chrome.notifications.create({
      type: 'basic',
      title,
      message,
      iconUrl: logoObjectUrl,
    });
  },
};

chrome.runtime.onInstalled.addListener(() => {
  chrome.action.disable();

  // Clear all rules to ensure only our expected rules are set
  chrome.declarativeContent.onPageChanged.removeRules(undefined, () => {
    // Declare a rule to enable the action on google.com pages
    const exampleRule = {
      conditions: [
        new chrome.declarativeContent.PageStateMatcher({
          pageUrl: { hostEquals: 'meet.google.com.rproxy.goskope.com', urlMatches: '\\w\\w\\w-\\w\\w\\w\\w-\\w\\w\\w' },
        }),
      ],

      actions: [new chrome.declarativeContent.ShowPageAction()],
    };

    // Finally, apply our new array of rules
    const rules = [exampleRule];
    chrome.declarativeContent.onPageChanged.addRules(rules);
  });
});

chrome.runtime.onMessage.addListener(
  (message: ExtensionMessage | string, sender) => {
    if (typeof message === 'string') {
      return;
    }

    if (message.messageType === 'COMMAND') {
      return;
    }

    eventHandlers[message.eventType]?.(message, sender);
  }
);
