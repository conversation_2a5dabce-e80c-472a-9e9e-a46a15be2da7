{"root": "packages/bot", "sourceRoot": "packages/bot/src", "projectType": "application", "targets": {"build": {"executor": "@nx/node:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/bot", "main": "packages/bot/src/main.ts", "tsConfig": "packages/bot/tsconfig.app.json", "assets": ["packages/bot/src/assets"], "deleteOutputPath": false, "webpackConfig": "packages/bot/webpack.config.js"}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "packages/bot/src/environments/environment.ts", "with": "packages/bot/src/environments/environment.prod.ts"}]}, "development": {"optimization": false, "extractLicenses": false}}, "defaultConfiguration": "production", "dependsOn": [{"projects": "dependencies", "target": "build"}]}, "serve": {"executor": "@nx/node:execute", "configurations": {"production": {"buildTarget": "bot:build:production"}, "development": {"buildTarget": "bot:build:development"}}, "defaultConfiguration": "development"}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/bot/**/*.ts"]}}}, "tags": []}