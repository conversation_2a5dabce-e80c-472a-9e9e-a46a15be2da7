import EventEmitter from 'events';

import { Inject, Injectable, InjectionToken, Provider } from 'injection-js';
import { authenticator } from 'otplib';
import { <PERSON><PERSON><PERSON>, <PERSON>ement<PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';

import {
  DatabaseBackend,
  LOGGER_BACKEND,
  Logger,
  LoggerBackend,
  MeetingCollection,
} from '@kurt/shared';

import { BROWSER } from './browser';
import { DATABASE_BACKEND } from './database-backend';
import {
  LanguageNotSupportedError,
  MeetingAlreadyJoinedError,
  MeetingAlreadyRecordingError,
  MeetingNotFoundError,
  MeetingNotRecordingError,
} from './errors';

import type { TranscriberScriptGlobalContext } from '@kurt/bot/transcriber-script';
import type {
  AudioRecordingDataEventDetail,
  AudioRecordingEndedEventDetail,
  ParticipantsListChangedEventDetail,
} from '@kurt/transcriber';

export interface MeetingParams {
  initialLanguage: SupportedLanguage;
  gmeetId: string;
  firebaseId: string;
}

const MEETING_PARAMS = new InjectionToken<MeetingParams>('Meeting Params');

export const createMeetingParamsProvider = (
  meetingParams: MeetingParams
): Provider => {
  return {
    provide: MEETING_PARAMS,
    useValue: meetingParams,
  };
};

export const enum MeetingStatus {
  None = 'N/A',
  Joining = 'Joining',
  Joined = 'Joined',
  Starting = 'Starting',
  Recording = 'Recording',
  Stopping = 'Stopping',
  Leaving = 'Leaving',
}

export type SupportedLanguage =
  | 'en'
  | 'es-es'
  | 'pt-br'
  | 'fr'
  | 'de'
  | 'es'
  | 'pt';

export const languages: ReadonlyMap<SupportedLanguage, string> = new Map([
  ['en', 'en-US'],
  ['es-es', 'es-ES'],
  ['es', 'es-ES'],
  ['pt-br', 'pt-BR'],
  ['pt', 'pt-PT'],
  ['fr', 'fr-FR'],
  ['de', 'de-DE'],
]);

export const MEETING_DESTROY_EVENT_NAME = 'destroy';

@Injectable()
export class Meeting extends EventEmitter {
  get language(): SupportedLanguage {
    return this._language;
  }

  get status() {
    return this._status;
  }

  get firebaseId() {
    return this._firebaseId;
  }

  private _language: SupportedLanguage;
  private _status = MeetingStatus.None;
  private _gmeetId: string;
  private _firebaseId: string;
  private _page?: Page;
  private _leaveTimeout?: ReturnType<typeof setTimeout>;
  private audioRecordingData!: Map<unknown, string[]>;
  private recordingStartedAt?: Date;
  private _isReauthenticating = false;

  // Utility function to replace deprecated waitForTimeout
  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  constructor(
    @Inject(MEETING_PARAMS) meetingParams: MeetingParams,
    @Inject(BROWSER) private browser: Browser,
    @Inject(DATABASE_BACKEND) private databaseBackend: DatabaseBackend,
    private logger: Logger,
    @Inject(LOGGER_BACKEND) private loggerBackend: LoggerBackend
  ) {
    super();

    this._language = meetingParams.initialLanguage;
    this._gmeetId = meetingParams.gmeetId;
    this._firebaseId = meetingParams.firebaseId;
  }

  async join(reauthenticate: boolean = true) {
    this.logger.log(`Joining ${this._gmeetId}. Language ${this.language}`);

    if (this._status !== MeetingStatus.None) {
      throw new MeetingAlreadyJoinedError(this._gmeetId);
    }

    this._status = MeetingStatus.Joining;

    this.logger.log('Opening new browser page');

    const context = this.browser.defaultBrowserContext();

    if (!this._page) {
      this._page = await context.newPage();
    } else {
      this.logger.log('Already open browser page');
    }

    this._page.on('error', async (error) => {
      this.logger.log('Page error detected.');
      this.logger.error(error);
      this.logger.log('Error occurred:' + JSON.stringify(error));

      let shouldDestroy = true;

      if (
        error.message.includes('Failed to load resource') &&
        error.message.includes('status of 403')
      ) {
        this.logger.log('403 error detected. Attempting re-authentication.');

        if (reauthenticate) {
          try {
            const authSuccess = await this.reAuthenticate();
            if (authSuccess) {
              this.logger.log(
                'Re-authentication successful, attempting to rejoin meeting'
              );
              try {
                await this.join(false);
                shouldDestroy = false;
                return;
              } catch (joinError) {
                this.logger.error(joinError as Error);
                this.logger.log(
                  'Failed to rejoin meeting after successful re-authentication'
                );
              }
            } else {
              this.logger.log('Re-authentication failed');
            }
          } catch (e) {
            this.logger.error(e as Error);
            this.logger.log('Re-authentication failed with exception');
          }
        } else {
          this.logger.log(
            'Re-authentication not attempted (reauthenticate=false)'
          );
        }
      }

      // Try to capture a screenshot before destroying the page
      try {
        const screenshot = await this.getFullPageScreenshot();
        if (screenshot) {
          const errorType = error.name || 'UnknownError';
          const timestamp = new Date().valueOf();
          const documentId = `error-${errorType}-${timestamp}`;

          // Convert base64 to data URL format
          const dataUrl = `data:image/jpeg;base64,${screenshot as string}`;

          await this.databaseBackend.saveErrorScreenshot(
            dataUrl,
            this._firebaseId || documentId
          );

          this.logger.log(
            `Error screenshot saved with ID: ${this._firebaseId || documentId}`
          );
        }
      } catch (screenshotError) {
        // Don't let screenshot errors affect the process
        this.logger.error(screenshotError as Error);
      }

      if (shouldDestroy) {
        this.logger.log(
          'Destroying meeting instance due to unrecoverable error'
        );
        this.destroy();
      }
    });

    // Handle console events to capture logs
    this._page?.on('console', async (message) => {
      if (message.type() === 'error') {
        if (
          message.text().includes('Failed to load resource') &&
          message.text().includes('status of 403')
        ) {
          this.logger.log(
            '403 error detected in console. Attempting re-authentication.'
          );
          this.logger.log('Error occurred:' + JSON.stringify(message.text()));

          let shouldDestroy = true;

          if (reauthenticate) {
            try {
              const authSuccess = await this.reAuthenticate();
              if (authSuccess) {
                this.logger.log(
                  'Re-authentication successful, attempting to rejoin meeting'
                );
                try {
                  await this.join(false);
                  shouldDestroy = false; // Don't destroy if we successfully rejoined
                  return;
                } catch (joinError) {
                  this.logger.error(joinError as Error);
                  this.logger.log(
                    'Failed to rejoin meeting after successful re-authentication'
                  );
                }
              } else {
                this.logger.log('Re-authentication failed');
              }
            } catch (e) {
              this.logger.error(e as Error);
              this.logger.log('Re-authentication failed with exception');
            }
          } else {
            this.logger.log(
              'Re-authentication not attempted (reauthenticate=false)'
            );
          }

          try {
            const screenshot = await this.getFullPageScreenshot();
            if (screenshot) {
              const timestamp = new Date().valueOf();
              const documentId = `error-console-${timestamp}`;

              // Convert base64 to data URL format
              const dataUrl = `data:image/jpeg;base64,${screenshot as string}`;

              await this.databaseBackend.saveErrorScreenshot(
                dataUrl,
                this._firebaseId || documentId
              );

              this.logger.log(
                `Console error screenshot saved with ID: ${
                  this._firebaseId || documentId
                }`
              );
            }
          } catch (screenshotError) {
            // Don't let screenshot errors affect the process
            this.logger.error(screenshotError as Error);
          }

          if (shouldDestroy) {
            this.logger.log(
              'Destroying meeting instance due to unrecoverable console error'
            );
            this.destroy();
          }
        }
      }
    });

    this.logger.log('New browser page opened');

    try {
      this.logger.log(
        `Navigating to https://meet.google.com.rproxy.goskope.com/${this._gmeetId}`
      );
      await this._page?.setBypassCSP(true);
      await this._page?.goto(
        `https://meet.google.com.rproxy.goskope.com/${this._gmeetId}`,
        {
          waitUntil: 'networkidle2',
        }
      );
      if (this._page) {
        await this.addScriptsAndFunctionsToPage(this._page);
      }
      await this.delay(5000);
      this.logger.log('Waiting for join button');
      await this._page?.waitForSelector('[jsname="bN97Pc"]', {
        visible: true,
        hidden: false,
      });
      await this.delay(5000);

      this.logger.log('Clicking join button');
      await this._page?.click('[jsname="Qx7uuf"]');

      this.logger.log('Waiting for meeting page to load');
      await this.delay(1000);

      const frameSelector = 'iframe';

      await this._page?.waitForSelector(frameSelector);

      this.logger.log('Meeting page loaded');
      this._status = MeetingStatus.Joined;

      await this._page?.evaluate(async () => {
        const transcriberContext =
          window as unknown as TranscriberScriptGlobalContext;
        const transcriberInstance = transcriberContext.transcriber;

        await transcriberInstance.initializeObservers();
      });
      if (this.language !== 'en') {
        this.changeLanguage(this.language);
      }
    } catch (e) {
      //if timeout error for bN97Pc then reauthenticate
      if (e instanceof Error && e.message.includes('TimeoutError')) {
        this.logger.log(
          'Timeout error detected. Attempting re-authentication.'
        );

        if (reauthenticate) {
          await this.triggerReauthentication();
        } else {
          this.logger.log(
            'Re-authentication not attempted (reauthenticate=false)'
          );
          this.logger.error(e);
          throw e;
        }
      } else {
        this.logger.log('Non-timeout error occurred:');
        if (reauthenticate) {
          await this.triggerReauthentication();
        } else {
          this.logger.log(
            'Re-authentication not attempted (reauthenticate=false)'
          );
        }
        this.logger.error(e as Error);
      }

      this.logger.log('Destroying meeting instance due to unrecoverable error');
      this.destroy();
    }
  }

  async leave(displayName?: string) {
    this.logger.log(`Leaving ${this._gmeetId}`);

    if (!this._page) {
      this.destroy();

      return;
    }

    if (this._status === MeetingStatus.Recording) {
      await this.stopRecording(displayName ?? 'Airbus Assistant');
    }

    if (this._status !== MeetingStatus.Joined) {
      throw new MeetingNotFoundError(this._gmeetId);
    }

    this._status = MeetingStatus.Leaving;

    await this.databaseBackend.updateMeetingLiveInfo({
      isLive: false,
      status: this._status,
      recordingLanguage: this.language,
    });

    try {
      await this._page.click('[jsname="CQylAd"]');
      if (this._page) {
        await this._page.close();
      }
    } catch (e) {
      this.logger.error(e as Error);
    }

    this._status = MeetingStatus.None;
    this.destroy();
  }

  async startRecording(supportedLanguage?: SupportedLanguage) {
    this.logger.log(
      `Start recording ${this._gmeetId}. Language ${this._language}.`
    );

    if (this.status === MeetingStatus.None) {
      await this.join();
    }

    if (this.status !== MeetingStatus.Joined) {
      throw new MeetingAlreadyRecordingError(this._gmeetId);
    }

    this._status = MeetingStatus.Starting;

    this.audioRecordingData = new Map();
    await this._page?.evaluate(async (language) => {
      const transcriberInstance = (
        window as unknown as TranscriberScriptGlobalContext
      ).transcriber;

      await transcriberInstance.startTranscription(language);
    }, this._language);

    this.changeLanguage(supportedLanguage ?? this.language);
    this.recordingStartedAt = new Date();
    this._status = MeetingStatus.Recording;
  }

  async stopRecording(displayName?: string) {
    this.logger.log(`Stop recording ${this._gmeetId}`);

    if (this.status !== MeetingStatus.Recording) {
      throw new MeetingNotRecordingError(this._gmeetId);
    }

    this._status = MeetingStatus.Stopping;

    try {
      await this._page?.evaluate(async (name) => {
        const transcriberInstance = (
          window as unknown as TranscriberScriptGlobalContext
        ).transcriber;

        await transcriberInstance.stopTranscription(name ?? 'Airbus Assistant');
      }, displayName);
    } catch (e) {
      this.logger.error(e as Error);

      // Try to capture a screenshot on error
      try {
        const screenshot = await this.getFullPageScreenshot();
        if (screenshot) {
          const error = e as Error;
          const errorType = error.name || 'UnknownError';
          const timestamp = new Date().valueOf();
          const documentId = `error-stop-recording-${errorType}-${timestamp}`;

          // Convert base64 to data URL format
          const dataUrl = `data:image/jpeg;base64,${screenshot as string}`;

          await this.databaseBackend.saveErrorScreenshot(
            dataUrl,
            this._firebaseId || documentId
          );

          this.logger.log(
            `Stop recording error screenshot saved with ID: ${
              this._firebaseId || documentId
            }`
          );
        }
      } catch (screenshotError) {
        // Don't let screenshot errors affect the process
        this.logger.error(screenshotError as Error);
      }

      this.saveAudioRecording({
        endedAt: new Date().toISOString() as unknown as Date,
        startedAt: (
          this.recordingStartedAt || new Date()
        ).toISOString() as unknown as Date,
      });
    }

    this._status = MeetingStatus.Joined;
  }

  async getCroppedScreenshot(): Promise<string | Buffer | undefined> {
    try {
      const element = await this._page?.waitForSelector('[jsname="E2KThb"]');
      const parentElement = (await element
        ?.getProperty('parentElement')
        .then((parent) =>
          parent.getProperty('parentElement')
        )) as ElementHandle<Element>;
      const width: number = await parentElement
        ?.getProperty('offsetWidth')
        .then(async (val) => await val.jsonValue()) as number;
      const height: number = await parentElement
        ?.getProperty('offsetHeight')
        .then(async (val) => await val.jsonValue()) as number;
      const x: number = await parentElement
        ?.getProperty('offsetLeft')
        .then(async (val) => await val.jsonValue()) as number;
      const y: number = await parentElement
        ?.getProperty('offsetTop')
        .then(async (val) => await val.jsonValue()) as number;

      this.logger.log(
        `Screenshot will be taken on: ${width} - ${height} | left: ${x} - top: ${y}`
      );

      const screenshot = await this._page?.screenshot({
        encoding: 'base64',
        quality: 70,
        type: 'jpeg',
        clip: {
          width: width,
          height: height,
          x: x,
          y: y,
        },
      });

      return screenshot;
    } catch {
      this.logger.log(
        'Screenshot could not be cropped , trying to get Full-Page one.'
      );

      return;
    }
  }

  async getFullPageScreenshot(): Promise<string | Buffer | undefined> {
    return await this._page?.screenshot({
      encoding: 'base64',
      quality: 70,
      type: 'jpeg',
    });
  }

  async screenshot(userEmail: string) {
    this.logger.log(`Screenshot for ${this._gmeetId}`);

    if (this.status !== MeetingStatus.Recording) {
      throw new MeetingNotRecordingError(this._gmeetId);
    }

    let screenshot = await this.getCroppedScreenshot();

    if (!screenshot) {
      screenshot = await this.getFullPageScreenshot();
      if (!screenshot) {
        return;
      }
    }

    // For regular screenshots, we don't need to convert to data URL format
    // because the createDocument method expects base64 string directly
    const documentId = `${userEmail}-${new Date().valueOf()}`;
    await this.databaseBackend.createDocument(
      MeetingCollection.Screenshots,
      documentId,
      {
        data: screenshot as string,
        startedAt: new Date(),
        person: userEmail,
        isSaved: false,
      }
    );

    return documentId;
  }

  async changeLanguage(language: SupportedLanguage) {
    this.logger.log(`Changing language to ${language}`);
    if (!languages.has(language)) {
      throw new LanguageNotSupportedError(language);
    }

    if (
      this.status !== MeetingStatus.Joined &&
      this.status !== MeetingStatus.Starting &&
      this.status !== MeetingStatus.Recording
    ) {
      throw new MeetingNotRecordingError(this._gmeetId);
    }

    try {
      this.logger.log('Changing language');
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      const languageValue = languages.get(language)!;
      this.logger.log(
        'Changing language to ' + language + ' with value ' + languageValue
      );

      this.logger.log('Opening menu');
      this._page?.evaluate(() =>
        document.querySelector<HTMLButtonElement>('[jsname="NakZHc"]')?.click()
      );
      // //log all menu buttons
      // if (menuButtons) {
      //   menuButtons.forEach((menuButtonI) => {
      //     this.logger.log("Menu Button change lag: " +  menuButtonI.id);
      //   });
      // }

      //get the second menu button

      this.logger.log('Menu button found ');

      this._page?.evaluate(() =>
        document.querySelector<HTMLElement>('[jsname="dq27Te"]')?.click()
      );
      //wait for 500ms
      await new Promise((r) => setTimeout(r, 500));
      this.logger.log('Settings button clicked ');
      this._page?.evaluate(() =>
        document.querySelectorAll<HTMLElement>('[jsname="z4Tpl"]')[3]?.click()
      );
      //wait for 500ms
      await new Promise((r) => setTimeout(r, 500));
      this.logger.log('CC button clicked ');
      // setTimeout(() => {
      this.logger.log('Opening language dialog');
      this._page?.evaluate(
        (value) =>
          document
            .querySelector<HTMLElement>(
              `[jsname="rymPhb"] [data-value="${value}"]`
            )
            ?.click(),
        languageValue
      );

      this.logger.log('Clicking language option' + languageValue);
      await new Promise((r) => setTimeout(r, 500));
      this.logger.log('Clicking apply button');
      this._page?.evaluate(() =>
        document.querySelectorAll<HTMLElement>('[jsname="YPqjbf"]')[1].click()
      );

      // setTimeout(() => {
      this._page?.evaluate(() =>
        document
          .querySelector<HTMLElement>('[data-mdc-dialog-action="close"]')
          ?.click()
      );

      await this._page?.evaluate((value) => {
        const transcriberInstance = (
          window as unknown as TranscriberScriptGlobalContext
        ).transcriber;

        transcriberInstance.setLanguage(value);
      }, language);

      // resolve();
      // }, 500);
      // }, 500);
      // });

      if (this._page) {
        await this.databaseBackend.updateMeetingLiveInfo({
          isLive: true,
          status: MeetingStatus.Recording,
          recordingLanguage: language,
        });
      }
      this._language = language;
    } catch (error) {
      this.logger.error(error as Error);
    }
  }

  async sendMessage(messageDetails: { message: string }) {
    await this._page?.evaluate((message: string) => {
      const transcriberInstance = (
        window as unknown as TranscriberScriptGlobalContext
      ).transcriber;

      transcriberInstance.sendMessage(message);
    }, messageDetails.message);
  }

  private async addScriptsAndFunctionsToPage(page: Page) {
    await Promise.all(
      Object.entries(this.databaseBackend)
        .filter(([, value]) => typeof value === 'function')
        .map(([key, value]) =>
          this._page?.exposeFunction(
            key,
            value as (...args: unknown[]) => unknown
          )
        )
    );
    await Promise.all(
      Object.entries(this.loggerBackend)
        .filter(([, value]) => typeof value === 'function')
        .map(([key, value]) =>
          this._page?.exposeFunction(
            key,
            value as (...args: unknown[]) => unknown
          )
        )
    );
    await page.exposeFunction('getFirebaseId', () => this._firebaseId);
    await page.exposeFunction('getGmeetId', () => this._gmeetId);
    await page.exposeFunction(
      'onParticipantsListChanged',
      (participantsList: ParticipantsListChangedEventDetail) => {
        if (this._leaveTimeout) {
          clearTimeout(this._leaveTimeout);
          this._leaveTimeout = undefined;
        }

        if (participantsList.length === 1) {
          this._leaveTimeout = setTimeout(() => {
            this.logger.log('Leaving because no participants');
            try {
              this.leave();
            } catch {}
          }, 5000);
        }
      }
    );
    await page.exposeFunction('onKicked', () => this.onKicked());
    await page.exposeFunction(
      'onAudioRecordingData',
      (event: AudioRecordingDataEventDetail) => {
        let streamData = this.audioRecordingData.get(event.stream);

        if (!streamData) {
          streamData = [];
          this.audioRecordingData.set(event.stream, streamData);
        }

        streamData.push(event.data);
      }
    );
    await page.exposeFunction(
      'onAudioRecordingEnded',
      (event: AudioRecordingEndedEventDetail) => {
        this.saveAudioRecording(event);
      }
    );
    await page.addScriptTag({
      path: `${process.cwd()}/dist/packages/bot/transcriber-script/main.js`,
    });
  }

  private saveAudioRecording(event: AudioRecordingEndedEventDetail) {
    const data = Array.from(this.audioRecordingData.values()).map(
      (streamData) => streamData.join('')
    );

    this.databaseBackend.saveAudioRecording({
      data,
      ...event,
    });
  }

  private onKicked() {
    this.saveAudioRecording({
      endedAt: new Date().toISOString() as unknown as Date,
      startedAt: (
        this.recordingStartedAt || new Date()
      ).toISOString() as unknown as Date,
    });
    this.destroy();
  }

  private destroy() {
    if (this._page && !this._page.isClosed()) {
      this._page.close();
    }

    this.databaseBackend.updateMeeting({ transcribedBy: null });
    this.databaseBackend.updateMeetingLiveInfo({
      isLive: false,
      status: MeetingStatus.None,
    });
    this._status = MeetingStatus.None;
    this.emit(MEETING_DESTROY_EVENT_NAME);
  }

  private async reAuthenticate() {
    // If already reauthenticating, return to prevent multiple attempts
    if (this._isReauthenticating) {
      this.logger.log(
        'Reauthentication already in progress, skipping duplicate attempt'
      );
      return false;
    }

    this._isReauthenticating = true;
    this.logger.log('Re-authenticating...');

    try {
      if (!this._page) {
        this.logger.log('No page to re-authenticate');
        return false;
      }

      await this._page.goto(
        'https://accounts.google.com/signin/v2/identifier',
        {
          waitUntil: 'networkidle2',
        }
      );

      this.logger.log('Waiting for username field.');
      await this._page.waitForSelector('#identifierId');
      await this._page.type('#identifierId', '<EMAIL>', {
        delay: 100,
      });
      await this._page.keyboard.press('Enter');

      this.logger.log('Waiting for onelogin page.');
      await this._page.waitForNavigation({ waitUntil: 'networkidle2' });

      await this._page.waitForSelector('input[id="username"]');
      await this._page.type('input[id="username"]', '<EMAIL>', {
        delay: 100,
      });
      await this._page.keyboard.press('Enter');

      this.logger.log('Waiting for password field.');
      await this._page.waitForSelector('input[type="password"]');
      await this._page.type('input[type="password"]', '.Vh3hcA5a;T6axX*uFok', {
        delay: 500,
      });
      await this._page.keyboard.press('Enter');

      this.logger.log('Waiting for OTP field.');
      await this._page.waitForSelector('#security-code');
      const token = authenticator.generate('BKQXLA5DQQQPI2ROKSSX2VVV3X3L6RYB');
      await this._page.type('#security-code', token, { delay: 100 });
      await this._page.keyboard.press('Enter');

      this.logger.log('Waiting for login success navigation.');
      await this._page.waitForNavigation({ waitUntil: 'networkidle2' });

      // Verify login was successful by checking for a Google-specific element
      try {
        // Wait for a short time to see if we get redirected to an error page
        await this.delay(2000);

        // Check if we're on an error page
        const errorElement = await this._page.$('div[data-error-code]');
        if (errorElement) {
          this.logger.log('Login failed: Error page detected');
          return false;
        }

        this.logger.log('Login successful');
        return true;
      } catch (e) {
        this.logger.error(e as Error);
        this.logger.log('Failed to verify login success');
        return false;
      }
    } catch (e) {
      this.logger.error(e as Error);
      this.logger.log('Reauthentication failed with error');
      return false;
    } finally {
      this._isReauthenticating = false;
    }
  }
  private async triggerReauthentication() {
    try {
      const authSuccess = await this.reAuthenticate();
      if (authSuccess) {
        this.logger.log(
          'Re-authentication successful, attempting to rejoin meeting'
        );
        try {
          await this.join(false);
          return;
        } catch (joinError) {
          this.logger.error(joinError as Error);
          this.logger.log(
            'Failed to rejoin meeting after successful re-authentication'
          );
        }
      } else {
        this.logger.log('Re-authentication failed');
      }
    } catch (e2) {
      this.logger.error(e2 as Error);
      this.logger.log(
        'Re-authentication failed with exception. Destroying the meeting instance.'
      );
    }
  }
}
