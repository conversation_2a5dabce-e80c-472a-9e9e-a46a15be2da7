import { Inject, Injectable } from 'injection-js';

import { <PERSON>Backend, Logger } from '@kurt/shared';

import { <PERSON><PERSON><PERSON> } from 'puppeteer';
import { <PERSON>t<PERSON>ontroller } from '../bot-controller';
import { BROWSER } from '../browser';
import { DATABASE_BACKEND } from '../database-backend';

@Injectable()
export class ErrorScreenshotUtil {
  constructor(
    @Inject(DATABASE_BACKEND) private databaseBackend: DatabaseBackend,
    @Inject(BROWSER) private browser: Browser
  ) {}

  /**
   * Captures a screenshot when an error occurs and saves it to the database
   * @param error The error that occurred
   * @param logger The logger instance
   * @param botController Optional BotController instance to get active meeting
   * @returns The document ID of the saved screenshot or undefined if screenshot couldn't be captured
   */
  async captureErrorScreenshot(
    error: Error,
    logger: Logger,
    botController?: BotController
  ): Promise<string | undefined> {
    try {
      logger.log(`Capturing error screenshot for error: ${error.message}`);

      // Try to find an active meeting if bot<PERSON>ontroller is provided
      let meetingId: string | undefined;

      if (botController) {
        // Get all active meetings from the controller
        const activeMeetings = botController['meetings'];
        if (activeMeetings && activeMeetings.size > 0) {
          // Get the first active meeting
          for (const [gmeetId, activeMeeting] of activeMeetings.entries()) {
            meetingId = activeMeeting.firebaseId;
            logger.log(
              `Found active meeting with ID: ${meetingId} (Google Meet ID: ${gmeetId})`
            );
            break;
          }
        } else {
          logger.log('No active meetings found in BotController');
        }
      }

      // Capture screenshots from all open pages
      const pages = await this.browser.pages();
      let screenshotTaken = false;

      for (const [index, page] of pages.entries()) {
        try {
          // Skip pages that are already closed
          if (page.isClosed()) continue;

          logger.log(
            `Capturing screenshot from page ${index + 1}/${pages.length}`
          );

          // Take a full page screenshot as a Buffer
          const screenshotBuffer = await page.screenshot({
            encoding: 'binary',
            quality: 70,
            type: 'jpeg',
            fullPage: true,
          });

          if (screenshotBuffer) {
            screenshotTaken = true;

            // Create error metadata
            const errorType = error.name || 'UnknownError';
            const timestamp = new Date().valueOf();

            // Convert buffer to base64 string with proper format for Firebase Storage
            // First convert Buffer to base64 string
            const base64String =
              Buffer.from(screenshotBuffer).toString('base64');
            // Then add the data URL prefix for JPEG
            const dataUrl = `data:image/jpeg;base64,${base64String}`;

            logger.log(
              `Captured error screenshot with meeting ID: ${
                meetingId || 'general-error'
              } and error type: ${errorType}`
            );

            // Save the screenshot to the database with error information
            await this.databaseBackend.saveErrorScreenshot(
              dataUrl,
              meetingId || 'general-error'
            );

            // Also save to local debug folder for immediate inspection
            try {
              await page.screenshot({
                path: `./debug/error-${errorType}-${timestamp}-page${index}.jpg`,
                type: 'jpeg',
                quality: 70,
              });
            } catch (debugError) {
              logger.error(debugError as Error);
            }
          }
        } catch (pageError) {
          logger.error(pageError as Error);
        }
      }

      if (!screenshotTaken) {
        logger.log('Failed to capture any error screenshots');
        return undefined;
      }

      return meetingId || 'general-error';
    } catch (screenshotError) {
      // Don't let screenshot errors propagate
      logger.error(screenshotError as Error);
      return undefined;
    }
  }
}

export const errorScreenshotUtilProvider = {
  provide: ErrorScreenshotUtil,
  useFactory: (databaseBackend: DatabaseBackend, browser: Browser) => {
    return new ErrorScreenshotUtil(databaseBackend, browser);
  },
  deps: [DATABASE_BACKEND, BROWSER],
};
