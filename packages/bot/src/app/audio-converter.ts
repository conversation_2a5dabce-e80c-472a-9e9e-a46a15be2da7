import { Readable } from 'stream';

import { Converter } from 'ffmpeg-stream';
import { Injectable } from 'injection-js';

@Injectable()
export class AudioConverter {
  async webmToMp3(files: string[]) {
    return new Promise<Buffer>((resolve) => {
      const converter = new Converter();

      for (const file of files) {
        const inputStream = converter.createInputStream({
          f: 'webm',
        });
        Readable.from(Buffer.from(file, 'binary')).pipe(inputStream);
      }

      const outputStream = converter.createOutputStream({
        f: 'mp3',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        filter_complex: `amix=inputs=${files.length}:duration=longest`,
      });
      const buffers: Buffer[] = [];

      outputStream.on('data', (buffer) => buffers.push(buffer as Buffer));

      outputStream.on('close', () => {
        resolve(Buffer.concat(buffers));
      });

      converter.run();
    });
  }
}
