import { InjectionToken, Provider } from 'injection-js';

import { DatabaseBackend } from '@kurt/shared';

import { environment } from '../../environments/environment';

import { FirebaseDatabaseBackend } from './firebase-database-backend';
import { NoopDatabaseBackend } from './noop-database-backend';

export const DATABASE_BACKEND = new InjectionToken<DatabaseBackend>(
  'Database Backend'
);

export const databaseBackendProvider: Provider = {
  provide: DATABASE_BACKEND,
  useClass: environment.production
    ? FirebaseDatabaseBackend
    : NoopDatabaseBackend,
};

export { provideEncryptData } from './firebase-database-backend';
