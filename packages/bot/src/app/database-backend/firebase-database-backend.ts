import { App } from 'firebase-admin/app';
import { UserRecord } from 'firebase-admin/auth';
import { DocumentReference, getFirestore } from 'firebase-admin/firestore';
import { getStorage } from 'firebase-admin/storage';
import { Inject, Injectable, InjectionToken, Provider } from 'injection-js';

import {
  DatabaseBackend,
  ExtensionEvent,
  Logger,
  Meeting,
  MeetingCollection,
  MeetingLiveInfo,
  Screenshot,
  SpeakerInfo,
  TaskOrRecord,
  Transcript,
} from '@kurt/shared';

import { AudioConverter } from '../audio-converter';
import { Encrypter } from '../encrypter';
import { FIREBASE_ADMIN_APP } from '../firebase-app';

export const ENCRYPT_DATA = new InjectionToken<boolean>('Encrypt data');

export const provideEncryptData = (encrypt: boolean): Provider => {
  return { provide: ENCRYPT_DATA, useValue: encrypt };
};

@Injectable()
export class FirebaseDatabaseBackend implements DatabaseBackend {
  private db = getFirestore(this.firebaseApp);
  private storage = getStorage(this.firebaseApp);
  private audioStorage = this.storage.bucket('kurt-audio-recordings');
  private errorStorage = this.storage.bucket('kurt-error-screenshots');
  private meetingDocumentRef!: DocumentReference<Meeting>;
  private meetingId!: string;

  constructor(
    @Inject(FIREBASE_ADMIN_APP) private firebaseApp: App,
    private user: UserRecord,
    private logger: Logger,
    @Inject(ENCRYPT_DATA) private encryptData: boolean,
    private audioConverter: AudioConverter,
    private encrypter: Encrypter,
  ) {}

  initialize = (meetingId: string) => {
    this.meetingId = meetingId;
    this.meetingDocumentRef = this.db
      .collection('meetings')
      .doc(this.meetingId) as DocumentReference<Meeting>;

    return Promise.resolve();
  };

  getCurrentUserDisplayName = (): Promise<string> => {
    return Promise.resolve(this.user.displayName || '');
  };

  getCurrentUserEmail = (): Promise<string> => {
    return Promise.resolve(this.user.email || '');
  };

  getMeeting = async (): Promise<Meeting | undefined> => {
    const meetingDocument = await this.meetingDocumentRef.get();

    return meetingDocument.data();
  };

  createMeeting = async (meetingToCreate: Meeting): Promise<Meeting> => {
    const amendedMeeting = {
      ...meetingToCreate,
      date: new Date(meetingToCreate.date),
    };

    if (meetingToCreate.lastRecording) {
      amendedMeeting.lastRecording = new Date(meetingToCreate.lastRecording);
    }

    await this.meetingDocumentRef.set(amendedMeeting);

    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const createdMeeting = (await this.getMeeting())!;

    return createdMeeting;
  };

  updateMeeting = async (updates: Partial<Meeting>): Promise<Meeting> => {
    const amendedMeeting = { ...updates };

    if (updates.date) {
      amendedMeeting.date = new Date(updates.date);
    }

    if (updates.lastRecording) {
      amendedMeeting.lastRecording = new Date(updates.lastRecording);
    }

    await this.meetingDocumentRef.update(amendedMeeting);

    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const updatedMeeting = (await this.getMeeting())!;

    return updatedMeeting;
  };

  updateMeetingLiveInfo = async (
    updates: Partial<MeetingLiveInfo>,
  ): Promise<void> => {
    this.logger.log(`Update meeting live info: ${JSON.stringify(updates)}`);

    const doc = await this.meetingDocumentRef.get();

    if (!doc.exists) {
      this.logger.log(`Meeting document ${this.meetingId} does not exist`);
      return;
    }

    const previousLiveInfo = doc.data()?.liveInfo;
    this.logger.log(`Previous live info: ${JSON.stringify(previousLiveInfo)}`);
    if (previousLiveInfo) {
      if (!updates.recordingLanguage) {
        updates.recordingLanguage = previousLiveInfo.recordingLanguage;
      }
      await doc.ref.update({
        liveInfo: {
          isLive: updates.isLive,
          status: updates.status,
          recordingLanguage: updates.recordingLanguage,
        },
      });
    } else {
      await doc.ref.update({
        liveInfo: {
          isLive: updates.isLive,
          status: updates.status,
          recordingLanguage: updates.recordingLanguage || 'en',
        },
      });
    }
    this.logger.log(`Live info now: ${JSON.stringify(updates)}`);
  };

  createDocument = async (
    collection: MeetingCollection,
    id: string,
    data: Transcript | TaskOrRecord | ExtensionEvent | Screenshot,
  ): Promise<void> => {
    const amendedData = { ...data, startedAt: new Date(data.startedAt) };

    if (
      this.encryptData &&
      (collection === MeetingCollection.Messages ||
        collection === MeetingCollection.Transcripts)
    ) {
      const { text, version } = await this.encrypter.encrypt(
        (data as Transcript).text,
      );

      (amendedData as Transcript).text = text;
      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
      (amendedData as any).keyVersion = version;
    }

    if (collection === MeetingCollection.Transcripts) {
      (amendedData as Transcript).endedAt = new Date(
        (data as Transcript).endedAt,
      );
    }

    await this.meetingDocumentRef
      .collection(collection)
      .doc(id)
      .set(amendedData);
  };

  createMeetingLiveInfo = async (
    collection: MeetingCollection,
    data: MeetingLiveInfo,
    id: string,
  ): Promise<void> => {
    const amendedData = { ...data };

    await this.meetingDocumentRef
      .collection(collection)
      .doc(id)
      .set(amendedData);
  };

  updateDocument = async (
    collection: MeetingCollection,
    id: string,
    data: Partial<Transcript>,
  ): Promise<void> => {
    const amendedData = { ...data };

    if (data.endedAt) {
      amendedData.endedAt = new Date(data.endedAt);
    }

    if (data.startedAt) {
      amendedData.startedAt = new Date(data.startedAt);
    }

    if (collection === MeetingCollection.Transcripts) {
      (amendedData as Transcript).endedAt = new Date(
        (data as Transcript).endedAt,
      );

      if (this.encryptData) {
        const { text, version } = await this.encrypter.encrypt(
          (data as Transcript).text,
        );

        (amendedData as Transcript).text = text;
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        (amendedData as any).keyVersion = version;
      }
    }

    await this.meetingDocumentRef
      .collection(collection)
      .doc(id)
      .update(amendedData);
  };

  addSpeakerInfo = async (data: SpeakerInfo): Promise<void> => {
    await this.meetingDocumentRef
      .collection(MeetingCollection.Speakers)
      .doc()
      .create({
        name: data.name,
        isMuted: data.isMuted,
        date: new Date(data.date),
        isSpeaking: data.isSpeaking,
      });
  };

  saveAudioRecording = async ({
    data,
    endedAt,
    startedAt,
  }: {
    data: string[];
    startedAt: Date;
    endedAt: Date;
  }) => {
    const file = await this.audioConverter.webmToMp3(data);
    const filename = `${
      this.meetingId
    }-${startedAt.valueOf()}-${endedAt.valueOf()}.mp3`;

    try {
      await this.meetingDocumentRef
        .collection('audioRecordings')
        .doc()
        .create({
          filename: filename,
          startedAt: new Date(startedAt),
          endedAt: new Date(endedAt),
        });
      await this.audioStorage.file(filename).save(file);
    } catch (e) {
      this.logger.error(e as Error);
    }
  };

  saveErrorScreenshot = async (data: string, meetingId: string) => {
    try {
      // Extract the base64 data from the data URL
      // Format is: data:image/jpeg;base64,/9j/4AAQSkZJRg...
      const matches = data.match(/^data:([A-Za-z-+/]+);base64,(.+)$/);

      if (!matches || matches.length !== 3) {
        this.logger.error(
          new Error('Invalid data URL format for error screenshot'),
        );
        return;
      }

      // Get the file extension from the mime type
      const mimeType = matches[1];
      const base64Data = matches[2];
      const extension = mimeType.split('/')[1] || 'png';

      // Convert base64 to buffer
      const buffer = Buffer.from(base64Data, 'base64');

      // Create a unique filename with the correct extension
      const timestamp = new Date().valueOf();
      const filename = `error-${timestamp}.${extension}`;

      this.logger.log(`Saving error screenshot to ${meetingId}/${filename}`);

      // Save the buffer to Firebase Storage
      await this.errorStorage.file(`${meetingId}/${filename}`).save(buffer, {
        metadata: {
          contentType: mimeType,
        },
      });

      this.logger.log(
        `Error screenshot saved successfully to ${meetingId}/${filename}`,
      );
    } catch (error) {
      this.logger.error(error as Error);
    }
  };
}
