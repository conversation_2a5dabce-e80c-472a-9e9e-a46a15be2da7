import { writeFile } from 'fs/promises';

import { UserRecord } from 'firebase-admin/auth';
import { Injectable } from 'injection-js';

import {
  DatabaseBackend,
  ExtensionEvent,
  Logger,
  Meeting,
  MeetingCollection,
  MeetingLiveInfo,
  Screenshot,
  SpeakerInfo,
  TaskOrRecord,
  Transcript,
} from '@kurt/shared';

import { AudioConverter } from '../audio-converter';
import { Encrypter } from '../encrypter';

@Injectable()
export class NoopDatabaseBackend implements DatabaseBackend {
  private meetingId!: string;

  constructor(
    private user: UserRecord,
    private logger: Logger,
    private audioConverter: AudioConverter,
    private encrypter: Encrypter,
  ) {}
  saveErrorScreenshot = async (
    data: string,
    meetingId: string,
  ): Promise<void> => {
    try {
      // Extract the base64 data from the data URL for logging purposes
      const matches = data.match(/^data:([A-Za-z-+/]+);base64,(.+)$/);

      if (!matches || matches.length !== 3) {
        this.logger.error(
          new Error('Invalid data URL format for error screenshot'),
        );
        return;
      }

      // Get the file extension from the mime type
      const mimeType = matches[1];
      const timestamp = new Date().valueOf();
      const extension = mimeType.split('/')[1] || 'png';
      const filename = `error-${timestamp}.${extension}`;

      this.logger.log(
        `Error screenshot would be saved to ${meetingId}/${filename} (NOOP)`,
      );
      await Promise.resolve();
    } catch (error) {
      this.logger.error(error as Error);
    }
  };

  updateMeetingLiveInfo = async (
    updates: Partial<MeetingLiveInfo>,
  ): Promise<void> => {
    this.logger.log(`updateMeetingLiveInfo: ${JSON.stringify(updates)}`);
    await Promise.resolve();
  };

  initialize = (meetingId: string) => {
    this.meetingId = meetingId;
    this.logger.log(`Meeting initialized ${meetingId}.`);

    return Promise.resolve();
  };

  getCurrentUserDisplayName = (): Promise<string> => {
    return Promise.resolve(this.user.displayName || '');
  };

  getCurrentUserEmail = (): Promise<string> => {
    return Promise.resolve(this.user.email || '');
  };

  getMeeting = async (): Promise<Meeting | undefined> => {
    return Promise.resolve({
      date: new Date(),
      id: this.meetingId,
      summary: '',
      title: '',
    });
  };

  createMeeting = async (meeting: Meeting): Promise<Meeting> => {
    this.logger.log(`Meeting created ${meeting.id}.`);

    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const createdMeeting = (await this.getMeeting())!;

    this.logger.log(JSON.stringify(meeting, undefined, 2));

    return createdMeeting;
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  updateMeeting = async (_updates: Partial<Meeting>): Promise<Meeting> => {
    this.logger.log(`Meeting updated ${this.meetingId}.`);

    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const updatedMeeting = (await this.getMeeting())!;

    this.logger.log(JSON.stringify(_updates, undefined, 2));

    return updatedMeeting;
  };

  createDocument = async (
    collection: MeetingCollection,
    _id: string,
    _data: Transcript | TaskOrRecord | ExtensionEvent | Screenshot,
  ): Promise<void> => {
    this.logger.log(`Document created ${collection}#${_id}
    ${JSON.stringify(_data, undefined, 2)}`);

    if (collection === MeetingCollection.Transcripts) {
      const encryptedText = await this.encrypter.encrypt(
        (_data as Transcript).text,
      );
      this.logger.log(`Key version: ${encryptedText.version || ''}`);
      this.logger.log(`Encrypted text: ${encryptedText.text}`);
    }

    await Promise.resolve();
  };

  updateDocument = async (
    collection: MeetingCollection,
    _id: string,
    _data: Partial<Transcript>,
  ): Promise<void> => {
    this.logger.log(`Document updated ${collection}.
    ${JSON.stringify(_data)}`);
    await Promise.resolve();
  };

  addSpeakerInfo = async (_updates: SpeakerInfo): Promise<void> => {
    this.logger.log(`Speaker info updated:
    ${JSON.stringify(_updates)}`);
    await Promise.resolve();
  };

  saveAudioRecording = async ({
    data,
  }: {
    data: string[];
    startedAt: Date;
    endedAt: Date;
  }) => {
    const mp3Data = await this.audioConverter.webmToMp3(data);

    writeFile('test.mp3', mp3Data);
  };
}
