import { format } from 'date-fns';
import { UserRecord } from 'firebase-admin/auth';
import { Inject, Injectable, Injector, ReflectiveInjector } from 'injection-js';
import { Browser } from 'puppeteer';

import { Logger, provideLoggerId } from '@kurt/shared';

import { BROWSER } from './browser';
import {
  databaseBackendProvider,
  provideEncryptData,
} from './database-backend';
import { MeetingAlreadyJoinedError, MeetingNotFoundError } from './errors';
import {
  MEETING_DESTROY_EVENT_NAME,
  Meeting,
  MeetingStatus,
  SupportedLanguage,
  createMeetingParamsProvider,
} from './meeting';

@Injectable()
export class BotController {
  private meetings: Map<string, Meeting> = new Map();
  private user!: UserRecord;
  private encryptData = false;
  constructor(
    private injector: Injector,
    private logger: Logger,
    @Inject(BROWSER) private browser: Browser
  ) {}

  async joinMeeting({
    gmeetId,
    firebaseId = `${gmeetId}-${format(new Date(), 'dd-MM-yyyy')}`,
    user,
    language = 'en',
    encrypt = false,
  }: {
    gmeetId: string;
    firebaseId?: string;
    user: UserRecord;
    language?: SupportedLanguage;
    encrypt: boolean;
  }) {
    if (this.meetings.has(gmeetId)) {
      throw new MeetingAlreadyJoinedError(gmeetId);
    }

    const meeting = this.initializeMeeting(
      user,
      firebaseId,
      gmeetId,
      language,
      encrypt
    );

    await meeting.join();
  }

  async leaveMeeting(gmeetId: string, displayName?: string) {
    const meeting = this.meetings.get(gmeetId);

    if (!meeting) {
      throw new MeetingNotFoundError(gmeetId);
    }
    if (displayName) {
      await meeting.leave(displayName);
    } else {
      await meeting.leave('Unknown user');
    }
  }

  async startRecording({
    gmeetId,
    firebaseId = `${gmeetId}-${format(new Date(), 'dd-MM-yyyy')}`,
    user,
    language = 'en',
    encrypt = false,
  }: {
    gmeetId: string;
    firebaseId?: string;
    user: UserRecord;
    language?: SupportedLanguage;
    encrypt: boolean;
  }) {
    let meeting = this.meetings.get(gmeetId);

    if (!meeting) {
      meeting = this.initializeMeeting(
        user,
        firebaseId,
        gmeetId,
        language,
        encrypt
      );
    }

    await meeting.startRecording();
  }

  async stopRecording(gmeetId: string) {
    const meeting = this.meetings.get(gmeetId);

    if (!meeting) {
      throw new MeetingNotFoundError(gmeetId);
    }

    await meeting.stopRecording();
  }

  async screenshot(gmeetId: string, userEmail: string) {
    const meeting = this.meetings.get(gmeetId);

    if (!meeting) {
      throw new MeetingNotFoundError(gmeetId);
    }

    return await meeting.screenshot(userEmail);
  }

  getStatus(
    gmeetId: string,
    firebaseId?: string
  ): { status: MeetingStatus; language?: string } {
    const meetingState = this.meetings.get(gmeetId);
    const unknownMeetingStatus = { status: MeetingStatus.None };

    if (!meetingState) {
      return unknownMeetingStatus;
    }

    const knownMeetingStatus = {
      status: meetingState.status,
      language: meetingState.language,
    };

    if (!firebaseId) {
      return knownMeetingStatus;
    }

    if (meetingState.firebaseId !== firebaseId) {
      return unknownMeetingStatus;
    }

    return knownMeetingStatus;
  }

  async changeLanguage(gmeetId: string, language: SupportedLanguage) {
    const meeting = this.meetings.get(gmeetId);

    if (!meeting) {
      throw new MeetingNotFoundError(gmeetId);
    }
    await this.stopRecording(gmeetId);

    try {
      await this.startRecording({
        gmeetId,
        firebaseId: meeting.firebaseId,
        user: this.user,
        language,
        encrypt: this.encryptData,
      });
    } catch (error) {
      this.logger.log(
        'error in change language' +
          JSON.stringify(error) +
          ' ' +
          (global.sessionStorage.getItem('user') || '').toString()
      );
      this.leaveMeeting(gmeetId);
    }
  }

  async sendMessage(gmeetId: string, messageDetails: { message: string }) {
    const meeting = this.meetings.get(gmeetId);

    if (!meeting) {
      throw new MeetingNotFoundError(gmeetId);
    }

    await meeting.sendMessage(messageDetails);
  }

  async debug() {
    try {
      const browser = this.browser;
      const pages = await browser.pages();
      const timestamp = new Date().valueOf();

      // Create debug directory if it doesn't exist
      await Promise.all(
        pages.map((page, index) =>
          page.screenshot({
            path: `./debug/debug-${timestamp}-${index}.jpg`,
            type: 'jpeg',
            quality: 70,
          })
        )
      );

      this.logger.log(`Captured ${pages.length} debug screenshots`);
      return true;
    } catch (error) {
      this.logger.error(error as Error);
      return false;
    }
  }

  /**
   * Get a meeting instance by its ID
   * @param gmeetId The Google Meet ID
   * @returns The meeting instance or undefined if not found
   */
  getMeeting(gmeetId: string): Meeting | undefined {
    return this.meetings.get(gmeetId);
  }

  private initializeMeeting(
    user: UserRecord,
    firebaseId: string,
    gmeetId: string,
    language: SupportedLanguage,
    encrypt: boolean
  ) {
    const injector = ReflectiveInjector.resolveAndCreate(
      [
        { provide: UserRecord, useValue: user },
        createMeetingParamsProvider({
          firebaseId,
          gmeetId,
          initialLanguage: language,
        }),
        provideLoggerId(gmeetId),
        Logger,
        databaseBackendProvider,
        Meeting,
        provideEncryptData(encrypt),
      ],
      this.injector
    );
    const meeting = injector.get(Meeting) as Meeting;

    this.meetings.set(gmeetId, meeting);
    this.user = user;
    this.encryptData = encrypt;
    meeting.on(MEETING_DESTROY_EVENT_NAME, () => {
      this.meetings.delete(gmeetId);
    });

    return meeting;
  }
}
