import { App, cert, initializeApp } from 'firebase-admin/app';
import { Auth, getAuth } from 'firebase-admin/auth';
import { Firestore, getFirestore } from 'firebase-admin/firestore';
import { InjectionToken, Provider } from 'injection-js';

import serviceAccount from '../../d-nbi-kurt-bba89557ffad.json';

export const FIREBASE_ADMIN_APP = new InjectionToken<App>('Firebase Admin App');

export const firebaseAdminAppProvider: Provider = {
  provide: FIREBASE_ADMIN_APP,
  useValue: initializeApp({
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-argument
    credential: cert(serviceAccount as any),
  }),
};

export const FIREBASE_ADMIN_AUTH = new InjectionToken<Auth>(
  'Firebase Admin Auth'
);

export const firebaseAdminAuthProvider: Provider = {
  provide: FIREBASE_ADMIN_AUTH,
  useFactory: (firebaseAdminApp: App) => getAuth(firebaseAdminApp),
  deps: [FIREBASE_ADMIN_APP],
};

export const FIREBASE_ADMIN_DATABASE = new InjectionToken<Firestore>(
  'Firebase Admin Database'
);

export const firebaseAdminDatabaseProvider: Provider = {
  provide: FIREBASE_ADMIN_DATABASE,
  useFactory: (firebaseAdminApp: App) => getFirestore(firebaseAdminApp),
  deps: [FIREBASE_ADMIN_APP],
};
