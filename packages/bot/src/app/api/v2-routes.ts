import { Router } from 'express';

import { BotController } from '../bot-controller';
import { SupportedLanguage } from '../meeting';

import { AuthenticatedRequest } from './auth-middleware';
import { createRoutesProvider } from './routes';

export const v2RoutesFactory = (botController: BotController) => {
  const router = Router();

  router.get('/:meeting/join', async (req, res, next) => {
    try {
      await botController.joinMeeting({
        gmeetId: req.params.meeting,
        firebaseId: req.query['documentId'] as string,
        user: (req as AuthenticatedRequest<typeof req>).user,
        language: req.query['lang'] as SupportedLanguage | undefined,
        encrypt: req.query.encrypt === 'true',
      });
      res.end();
    } catch (e) {
      next(e);
    }
  });

  router.get('/:meeting/leave', async (req, res, next) => {
    try {
      await botController.leaveMeeting(
        req.params.meeting,
        (req as AuthenticatedRequest<typeof req>).user.displayName
      );
      res.end();
    } catch (e) {
      next(e);
    }
  });

  router.get('/:meeting/start-recording', async (req, res, next) => {
    try {
      await botController.startRecording({
        gmeetId: req.params.meeting,
        firebaseId: req.query['documentId'] as string,
        user: (req as AuthenticatedRequest<typeof req>).user,
        language: req.query['lang'] as SupportedLanguage | undefined,
        encrypt: req.query.encrypt === 'true',
      });
      res.end();
    } catch (e) {
      next(e);
    }
  });

  router.get('/:meeting/stop-recording', async (req, res, next) => {
    try {
      await botController.stopRecording(req.params.meeting);
      res.end();
    } catch (e) {
      next(e);
    }
  });

  router.get('/:meeting/screenshot', async (req, res, next) => {
    try {
      const id = await botController.screenshot(
        req.params.meeting,
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        (req as AuthenticatedRequest<typeof req>).user.email!
      );
      res.send({ id });
      res.end();
    } catch (e) {
      next(e);
    }
  });

  router.get('/:meeting/status', (req, res) =>
    res.send(
      botController.getStatus(
        req.params.meeting,
        req.query['documentId'] as string
      )
    )
  );

  router.get('/debug', async (_req, res) => {
    await botController.debug();

    res.end();
  });

  router.put('/:meeting/language', async (req, res) => {
    await botController.changeLanguage(
      req.params.meeting,
      req.body as SupportedLanguage
    );

    res.send(botController.getStatus(req.params.meeting));
  });

  router.post('/:meeting/message', async (req, res) => {
    await botController.sendMessage(
      req.params.meeting,
      req.body as { message: string }
    );

    res.end();
  });

  return {
    prefix: '/api/v2',
    router,
  };
};

export const v2RoutesProvider = createRoutesProvider({
  useFactory: v2RoutesFactory,
  deps: [BotController],
});
