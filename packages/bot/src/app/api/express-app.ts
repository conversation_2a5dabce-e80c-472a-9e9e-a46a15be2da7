import bodyParser from 'body-parser';
import cors from 'cors';
import express, { ErrorRequestHandler, Express, RequestHandler } from 'express';
import { UserRecord } from 'firebase-admin/auth';
import { InjectionToken, Provider } from 'injection-js';

import { AUTH_MIDDLEWARE } from './auth-middleware';
import { ERROR_HANDLER_MIDDLEWARE } from './error-handler-middleware';
import { Route, ROUTES } from './routes';

export const EXPRESS_APP = new InjectionToken<Express>('Express App');

export const expressAppProvider: Provider = {
  provide: EXPRESS_APP,
  useFactory: (
    authMiddleware: RequestHandler,
    errorHandlerMiddleware: ErrorRequestHandler,
    routes: Route[]
  ) => {
    const app = express();

    app.use(cors());
    app.use(bodyParser.text());
    app.use(bodyParser.json());
    app.use(authMiddleware);

    for (const route of routes) {
      app.use(route.prefix, route.router);
    }

    app.use(errorHandlerMiddleware);

    return app;
  },
  deps: [AUTH_MIDDLEWARE, ERROR_HANDLER_MIDDLEWARE, ROUTES, UserRecord],
};
