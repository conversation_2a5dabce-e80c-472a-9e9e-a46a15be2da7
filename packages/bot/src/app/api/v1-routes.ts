import { format } from 'date-fns';
import { Request, Router } from 'express';
import { Provider } from 'injection-js';

import { BotController } from '../bot-controller';

import { AuthenticatedRequest } from './auth-middleware';
import { createRoutesProvider, Route } from './routes';

export const v1RoutesFactory = (botController: BotController): Route => {
  const router = Router();

  router.get('/:meeting/join', async (req, res, next) => {
    try {
      await botController.joinMeeting({
        gmeetId: req.params.meeting,
        firebaseId: generateDocumentIdForMeeting(req),
        user: (req as AuthenticatedRequest<typeof req>).user,
        encrypt: req.query.encrypt === 'true',
      });
      res.end();
    } catch (e) {
      next(e);
    }
  });

  router.get('/:meeting/leave', async (req, res, next) => {
    try {
      await botController.leaveMeeting(req.params.meeting, (req as AuthenticatedRequest<typeof req>).user.displayName,);
      res.end();
    } catch (e) {
      next(e);
    }
  });

  router.get('/:meeting/start-recording', async (req, res, next) => {
    try {
      await botController.startRecording({
        gmeetId: req.params.meeting,
        firebaseId: generateDocumentIdForMeeting(req),
        user: (req as AuthenticatedRequest<typeof req>).user,
        encrypt: req.query.encrypt === 'true',
      });
      res.end();
    } catch (e) {
      next(e);
    }
  });

  router.get('/:meeting/stop-recording', async (req, res, next) => {
    try {
      await botController.stopRecording(req.params.meeting);
      res.end();
    } catch (e) {
      next(e);
    }
  });

  router.get('/:meeting/screenshot', async (req, res, next) => {
    try {
      const id = await botController.screenshot(
        req.params.meeting,
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        (req as AuthenticatedRequest<typeof req>).user.email!
      );
      res.send({ id });
      res.end();
    } catch (e) {
      next(e);
    }
  });

  router.get('/:meeting/status', (req, res) =>
    res.send(botController.getStatus(req.params.meeting).status)
  );

  router.get('/debug', async (_req, res) => {
    await botController.debug();
    res.end();
  });

  return {
    prefix: '/api',
    router,
  };
};

export const v1RoutesProvider: Provider = createRoutesProvider({
  useFactory: v1RoutesFactory,
  deps: [BotController],
});

function generateDocumentIdForMeeting(
  req: Request<{ meeting: string }>
): string {
  return `${req.params.meeting}-${format(new Date(), 'yyyy-MM-dd')}`;
}
