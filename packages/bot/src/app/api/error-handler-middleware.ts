import { <PERSON>rror<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { InjectionToken, Provider } from 'injection-js';

import { Logger } from '@kurt/shared';

import { <PERSON>t<PERSON>ontroller } from '../bot-controller';
import { BotError, MeetingNotFoundError } from '../errors';
import { ErrorScreenshotUtil } from '../utils/error-screenshot';

export const ERROR_HANDLER_MIDDLEWARE = new InjectionToken<ErrorRequestHandler>(
  'Error Handler Middleware',
);

export const errorHandlerMiddlewareProvider: Provider = {
  provide: ERROR_HANDLER_MIDDLEWARE,
  useFactory: (
    botController: <PERSON><PERSON><PERSON>ontroller,
    errorScreenshotUtil: ErrorScreenshotUtil,
    logger: Logger,
  ) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    return (async (error, req, res, _next) => {
      try {
        const meetingId = req.params.meeting;
        if (meetingId) {
          const meeting = botController.getMeeting(meetingId);
          if (meeting) {
            await errorScreenshotUtil.captureErrorScreenshot(
              error as Error,
              logger,
            );
          }
        }
      } catch (screenshotError) {
        logger.error(screenshotError as Error);
      }
      if (error instanceof MeetingNotFoundError) {
        res.status(409).send(error.message);
      } else if (error instanceof BotError) {
        res.status(404).send(error.message);
      } else {
        const nativeError = error as Error;

        res.status(500).send({
          message: nativeError.message,
          stack: nativeError.stack,
        });
      }
    }) as ErrorRequestHandler;
  },
  deps: [BotController, ErrorScreenshotUtil, Logger],
};
