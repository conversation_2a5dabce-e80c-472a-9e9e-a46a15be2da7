import { Request, RequestHandler } from 'express';
import { Auth, UserRecord } from 'firebase-admin/auth';
import { InjectionToken, Provider } from 'injection-js';

import { Logger } from '@kurt/shared';

import { environment } from '../../environments/environment';
import { FIREBASE_ADMIN_AUTH } from '../firebase-app';

export const AUTH_MIDDLEWARE = new InjectionToken<RequestHandler>(
  'Auth Middleware'
);

export type AuthenticatedRequest<T extends Request> = T & {
  user: UserRecord;
};

export const authMiddlewareProvider: Provider = {
  provide: AUTH_MIDDLEWARE,
  useFactory: (firebaseAdminAuth: Auth, logger: Logger) => {
    return (async (req, res, next) => {
      if (!environment.production) {
        (req as AuthenticatedRequest<typeof req>).user = {
          displayName: 'mock',
          email: 'mock',
        } as UserRecord;
        next();

        return;
      }

      const idToken = req.headers.authorization;

      if (!idToken) {
        res.status(401);
        res.end();

        return;
      }

      try {
        const decodedToken = await firebaseAdminAuth.verifyIdToken(idToken);
        const user = await firebaseAdminAuth.getUser(decodedToken.uid);

        (req as AuthenticatedRequest<typeof req>).user = user;
        next();
      } catch (e) {
        logger.error(e as Error);
        res.status(401);
        res.end();

        return;
      }
    }) as RequestHandler;
  },
  deps: [FIREBASE_ADMIN_AUTH, Logger],
};
