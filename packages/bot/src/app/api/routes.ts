import { Router } from 'express';
import { FactoryProvider, InjectionToken, Provider } from 'injection-js';

export interface Route {
  prefix: string;
  router: Router;
}

export const ROUTES = new InjectionToken<Route[]>('Routes');

export const createRoutesProvider = ({
  useFactory,
  deps,
}: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  useFactory: (...args: any[]) => Route;
  deps: FactoryProvider['deps'];
}): Provider => {
  return { provide: ROUTES, useFactory, deps, multi: true };
};
