export class BotError extends <PERSON><PERSON>r {}

export class MeetingNotFoundError extends BotError {
  constructor(meeting: string) {
    super(`Meeting not found. ${meeting}`);
  }
}

export class MeetingAlreadyJoinedError extends BotError {
  constructor(meeting: string) {
    super(`Meeting already joined. ${meeting}`);
  }
}

export class MeetingAlreadyRecordingError extends BotError {
  constructor(meeting: string) {
    super(`Meeting already recording. ${meeting}`);
  }
}

export class LanguageNotSupportedError extends BotError {
  constructor(language: string) {
    super(`Language not supported. ${language}`);
  }
}

export class MeetingNotRecordingError extends BotError {
  constructor(meeting: string) {
    super(`Meeting is not being recorded. ${meeting}`);
  }
}
