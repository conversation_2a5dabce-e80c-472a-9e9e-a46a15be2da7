import { Express } from 'express';
import { ReflectiveInjector } from 'injection-js';
import 'reflect-metadata';

import { Logger, provideLoggerBackend, provideLoggerId } from '@kurt/shared';

import { UserRecord } from 'firebase-admin/auth';

import {
  authMiddlewareProvider,
  errorHandlerMiddlewareProvider,
  EXPRESS_APP,
  expressAppProvider,
  v1RoutesProvider,
  v2RoutesProvider,
} from './app/api';
import { AudioConverter } from './app/audio-converter';
import { BotController } from './app/bot-controller';
import { provideBrowser } from './app/browser';
import {
  databaseBackendProvider,
  provideEncryptData,
} from './app/database-backend';
import { Encrypter } from './app/encrypter';
import {
  firebaseAdminAppProvider,
  firebaseAdminAuthProvider,
  firebaseAdminDatabaseProvider,
} from './app/firebase-app';
import { loggerBackend } from './app/logger-backend';
import {
  ErrorScreenshotUtil,
  errorScreenshotUtilProvider,
} from './app/utils/error-screenshot';

let botController: BotController | null = null;
let errorScreenshotUtil: ErrorScreenshotUtil | null = null;

process.on('uncaughtException', async (err) => {
  if (logger) {
    logger.error(err);

    try {
      // First try to capture screenshots with the error screenshot utility
      if (errorScreenshotUtil) {
        const screenshotId = await errorScreenshotUtil.captureErrorScreenshot(
          err,
          logger,
          botController || undefined,
        );
        if (screenshotId) {
          logger.log(`Captured error screenshots with ID: ${screenshotId}`);
        }
      }

      // Also try to capture debug screenshots from the bot controller
      if (botController) {
        await botController.debug();
        logger.log('Captured debug screenshots for uncaught exception');
      }
    } catch (screenshotError) {
      logger.error(screenshotError as Error);
    }

    return;
  }

  // eslint-disable-next-line no-console
  console.error(err);
});

const loggerInjector = ReflectiveInjector.resolveAndCreate([
  provideLoggerBackend(loggerBackend),
  Logger,
  provideLoggerId('Root'),
]);
const logger = loggerInjector.get(Logger) as Logger;

const browserProvider = await provideBrowser(logger);
const injector = ReflectiveInjector.resolveAndCreate(
  [
    {
      provide: UserRecord,
      useValue: {
        displayName: 'system',
        email: '<EMAIL>',
      } as UserRecord,
    },

    provideEncryptData(false),

    // Firebase providers
    firebaseAdminAppProvider,
    firebaseAdminAuthProvider,
    firebaseAdminDatabaseProvider,

    // Core services
    databaseBackendProvider,
    browserProvider,
    AudioConverter,
    Encrypter,

    // Utility providers
    errorScreenshotUtilProvider,

    // Controllers and middleware
    BotController,
    authMiddlewareProvider,
    errorHandlerMiddlewareProvider,

    // Express app and routes
    expressAppProvider,
    v1RoutesProvider,
    v2RoutesProvider,
  ],
  loggerInjector,
);
const expressApp = injector.get(EXPRESS_APP) as Express;

botController = injector.get(BotController) as BotController;
errorScreenshotUtil = injector.get(ErrorScreenshotUtil) as ErrorScreenshotUtil;

const port = process.env.PORT || 3333;
const server = expressApp.listen(port, () => {
  logger.log(`Listening at http://localhost:${port}/api`);
});

server.on('error', async (error: Error) => {
  logger.error(error);

  try {
    // First try to capture screenshots with the error screenshot utility
    if (errorScreenshotUtil) {
      const screenshotId = await errorScreenshotUtil.captureErrorScreenshot(
        error,
        logger,
        botController || undefined,
      );
      if (screenshotId) {
        logger.log(`Captured error screenshots with ID: ${screenshotId}`);
      }
    }

    // Also try to capture debug screenshots from the bot controller
    if (botController) {
      await botController.debug();
      logger.log('Captured debug screenshots for server error');
    }
  } catch (screenshotError) {
    logger.error(screenshotError as Error);
  }
});
