# bot

This is application exposes an API using [express](https://expressjs.com/) and controls a browser using [Puppeteer](https://pptr.dev/) in order to extract information from Google Meet.

In the [transcriber-script](./transcriber-script/README.md) directory we initialize the script that is injected to the page.

## API

### Auth middleware

This is an [express middleware](https://expressjs.com/en/guide/writing-middleware.html) that extracts the authentication token from the requests' headers and uses it to retrieve the requestor's account information from firebase.

### Error handler middleware

This is an [express middleware](https://expressjs.com/en/guide/writing-middleware.html) that catches errors and sets an appropriate HTTP status code before sending it back to the client.

### Routes

We have two versions of the api (v1-routes, v2-routes).

The routes defined in v1-routes have no prefix (/api/endpoint) and are used by the older versions of the mobile apps.

The routes defined in v2-routes are prefixed with v2 (/api/v2/endpoint) and are used by the newer versions of the mobile apps.

They are both registered using a [multi-provider](https://blog.thoughtram.io/angular2/2015/11/23/multi-providers-in-angular-2.html).

### Express app

Here we initialize the [express](https://expressjs.com/) with the routes and middlewares that we use.

See also:

- [cors](https://expressjs.com/en/resources/middleware/cors.html)
- [body-parser](http://expressjs.com/en/resources/middleware/body-parser.html)

## Database backend

We have two different implementations for the database backend service.

The FirebaseDatabaseBackend is used when running in production mode and it persists the data in Firebase.

The NoopDatabaseBackend is used in development mode and only logs the data to the console.

## Audio converter

This service is used to convert the separate (in reality there are 3 but this can handle any number) webm audio files we extract from the Google Meet page into one mp3 file.

## Browser

This service initializes the browser instance using [Puppeteer](https://pptr.dev/). After the browser starts it logs in to Google with 2FA using an OTP that we generate ourselves with [otplib](https://github.com/yeojz/otplib).

## Encrypter

This service is used to encrypt text content (eg. captions) using Google Cloud's KMS.

## Errors

Here we define the custom errors that we throw in the application. They all extend The BotError class to make the implementation of the [error handler middleware](#error-handler-middleware) simpler.

## Firebase app

Initialization of the firebase admin app.

## Logger backend

Logger backend implementation that writes messages to stdout/stderr.

## Meeting

This service manages the state of the recording of a meeting.

When the bot is requested to join a meeting we create a new instance specifically for that meeting. This opens a new tab which navigates to the meeting's URL, injects the [client-side script](./transcriber-script/README.md) to the page and exposes functions that the client-side script uses to run server-side code.

It also provides methods to interact with the page in order to join/leave, start/stop recording, change the language, take a screenshot and send a chat message.

## Bot controller

This service is responsible for creating [Meeting service instances](#meeting), keeping track of all the meetings the bot has joined and delegates commands to the [Meeting service instances](#meeting).
