import { DatabaseBackend, LoggerBackend } from '@kurt/shared';
import {
  AUDIO_RECORDING_DATA_EVENT_NAME,
  AUDIO_RECORDING_ENDED_EVENT_NAME,
  AudioRecordingDataEventDetail,
  AudioRecordingEndedEventDetail,
  createTranscriber,
  PARTICIPANTS_LIST_CHANGED_EVENT_NAME,
  ParticipantsListChangedEventDetail,
  Transcriber,
} from '@kurt/transcriber';

export interface TranscriberScriptGlobalContext
  extends DatabaseBackend,
    LoggerBackend {
  getFirebaseId: () => Promise<string>;
  getGmeetId: () => Promise<string>;
  transcriber: Transcriber;
  onParticipantsListChanged: (
    participantsList: ParticipantsListChangedEventDetail
  ) => void;
  // leaveMeeting: () => void;
  onKicked: () => void;
  onAudioRecordingData: (event: AudioRecordingDataEventDetail) => void;
  onAudioRecordingEnded: (event: AudioRecordingEndedEventDetail) => void;
}

const globalContext = window as unknown as TranscriberScriptGlobalContext;

const {
  createDocument,
  createMeeting,
  getCurrentUserDisplayName,
  getCurrentUserEmail,
  getMeeting,
  initialize,
  updateMeetingLiveInfo,
  saveAudioRecording,
  updateDocument,
  updateMeeting,
  addSpeakerInfo,
  saveErrorScreenshot,
} = globalContext;
const databaseBackend: DatabaseBackend = {
  createDocument,
  createMeeting,
  updateMeetingLiveInfo,
  getCurrentUserDisplayName,
  getCurrentUserEmail,
  getMeeting,
  initialize,
  saveAudioRecording,
  updateDocument,
  updateMeeting,
  addSpeakerInfo,
  saveErrorScreenshot,
};
const { log, error } = globalContext;
const loggerBackend: LoggerBackend = { log, error };
const meetingDocumentId = await globalContext.getFirebaseId();
const gmeetId = await globalContext.getGmeetId();
const transcriber = createTranscriber({
  databaseBackend,
  loggerBackend,
  meetingDocumentId,
  gmeetId,
});

transcriber.addEventListener(PARTICIPANTS_LIST_CHANGED_EVENT_NAME, (event) => {
  globalContext.onParticipantsListChanged(
    (event as CustomEvent<ParticipantsListChangedEventDetail>).detail
  );
});

transcriber.addEventListener(AUDIO_RECORDING_DATA_EVENT_NAME, (event) => {
  globalContext.onAudioRecordingData(
    (event as CustomEvent<AudioRecordingDataEventDetail>).detail
  );
});

transcriber.addEventListener(AUDIO_RECORDING_ENDED_EVENT_NAME, (event) => {
  globalContext.onAudioRecordingEnded(
    (event as CustomEvent<AudioRecordingEndedEventDetail>).detail
  );
});

const kickMutationObserver = new MutationObserver(() => {
  if (document.querySelector('[jsname="c6xFrd"]')) {
    kickMutationObserver.disconnect();
    globalContext.onKicked();
  }
});

kickMutationObserver.observe(document.body, {
  childList: true,
  subtree: true,
});

globalContext.transcriber = transcriber;
