# bot-transcriber-script

This library is used to build the script that is injected to the Google Meet page by the bot.

It creates a database backend implementation from the functions that the bot has exposed.

It creates a logger backend implementation from the functions that the bot has exposed.

It uses the functions exposed by the bot to create an instance of the [transcriber](../../transcriber/README.md#transcriber-1).
