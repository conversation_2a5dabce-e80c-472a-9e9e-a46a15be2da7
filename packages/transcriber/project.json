{"root": "packages/transcriber", "sourceRoot": "packages/transcriber/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/transcriber", "main": "packages/transcriber/src/index.ts", "tsConfig": "packages/transcriber/tsconfig.lib.json", "assets": ["packages/transcriber/*.md"]}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/transcriber/**/*.ts"]}}}, "tags": []}