# transcriber

## Content

This library provides services for extracting information from a Google Meet page. From the library's entry point we export all the types and constants the consuming applications might need and a function that creates a new instance of the transcriber service.

## Services

All the services extend the [EventTarget](https://developer.mozilla.org/en-US/docs/Web/API/EventTarget) class in order to dispatch events.

### Transcriber

This is the main service of this library. It controls all the services that extract the information from the Google Meet page and re-emits all events.

It also enables captions.

### AudioRecorder

This service finds all **audio** elements on the page and records their streams using a [MediaRecorder](https://developer.mozilla.org/en-US/docs/Web/API/MediaRecorder) for each stream. We can't use one MediaRecorder to record all the streams due to a browser limitation.

The Media Recorders are configured to emit audio data every 1 minute to work around a Chrome Devtools limitation which affected the bot on long meetings.

When the Media Recorders emit data we use a [FileReader][https://developer.mozilla.org/en-us/docs/web/api/filereader] to convert the [Blob](https://developer.mozilla.org/en-US/docs/Web/API/Blob) to a binary string. We need this conversion to work around a Chrome Devtools limitation (only serializable data is allowed to be transfered from the client to the server) which affects the bot.

### CaptionsObserver

This service uses a [MutationObserver](https://developer.mozilla.org/en-US/docs/Web/API/MutationObserver) to extract captions whenever they are added to the page.

Whenever a caption is added we need to find its container in order to identify the speaker.

A caption's content might be updated while it's still on the page so we debounce the processing by using setTimeout. We add the caption's element and the id of the setTimeout call to a [WeakMap](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/WeakMap).

This service is also responsible for identifying voice commands. The [VoiceCommandMatcher](#voicecommandmatcher) is used to provide multilingual support.

### ChatObserver

This service uses a [MutationObserver](https://developer.mozilla.org/en-US/docs/Web/API/MutationObserver) to extract chat messages whenever they are added to the page.

Messages posted by the bot are ignored.

Supports commands by prefixing the messages with `tsk:` (task) or `ftr:` (record).

It opens (and then closes) the chat panel when initialized to start loading the chat messages.

### Database

This service is responsible for pre-processing the data before we persist them.

It expects a backend implementation that will handle the job of persisting the data.

### ParticipantsListObserver

This service uses a [MutationObserver](https://developer.mozilla.org/en-US/docs/Web/API/MutationObserver) to extract the participants list from the page and track joins and leaves.

Here we actually use two MutationObservers because in order for the page to load the participants list we need to first open the panel (and then close it). So we use one MutationObserver to be notified of when the participants list initially loads and then the second one to track when participants join or leave.

Because Google Meet makes a lot of changes to the list when someone joins or leaves we debounce the processing of the changes and remove all duplicate entries.

### PresentationObserver

This service uses a [MutationObserver](https://developer.mozilla.org/en-US/docs/Web/API/MutationObserver) to find all video elements and add a button to each of them to extract screenshots.

It is only used by the Chrome Extension.

## Utilities

### CustomEvent

A [CustomEvent](https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent/CustomEvent) polyfill

### VoiceCommandMatcher

This class is used to check if a caption contains a voice command and extract the content.

It is initialized with the hotword to look for and an array of hotwords for the `For the record` command in order to get multilingual support.
