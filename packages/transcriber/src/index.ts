import { ReflectiveInjector } from 'injection-js';
import 'reflect-metadata';

import {
  DatabaseBackend,
  Logger,
  LoggerBackend,
  provideDatabaseBackend,
  provideLoggerBackend,
  provideLoggerId
} from '@kurt/shared';

import { AudioRecorder } from './lib/audio-recorder';
import { CaptionsObserver } from './lib/captions-observer';
import { ChatObserver } from './lib/chat-observer';
import './lib/custom-event';
import { Database } from './lib/database';
import { provideMeetingDocumentId } from './lib/meeting-document-id';
import { ParticipantsListObserver } from './lib/participants-list-observer';
import { PresentationObserver } from './lib/presentation-observer';
import { SpeakerRecognizer } from './lib/speaker-recognizer';
import { Transcriber } from './lib/transcriber';

export {
  AudioRecordingDataEventDetail, AudioRecordingEndedEventDetail, AUDIO_RECORDING_DATA_EVENT_NAME, AUDIO_RECORDING_ENDED_EVENT_NAME
} from './lib/audio-recorder';
export {
  NotificationEventDetail,
  NOTIFICATION_EVENT_NAME, ParticipantsListChangedEventDetail, PARTICIPANTS_LIST_CHANGED_EVENT_NAME,
  TRANSCRIPTION_STARTED_EVENT_NAME, TRANSCRIPTION_STOPPED_EVENT_NAME, type Transcriber
} from './lib/transcriber';


export const createTranscriber = ({
  meetingDocumentId,
  databaseBackend,
  loggerBackend,
  gmeetId,
}: {
  meetingDocumentId: string;
  databaseBackend: DatabaseBackend;
  loggerBackend: LoggerBackend;
  gmeetId: string;
}) => {
  const injector = ReflectiveInjector.resolveAndCreate([
    AudioRecorder,
    CaptionsObserver,
    ChatObserver,
    Database,
    provideDatabaseBackend(databaseBackend),
    provideMeetingDocumentId(meetingDocumentId),
    ParticipantsListObserver,
    SpeakerRecognizer,
    PresentationObserver,
    Transcriber,
    Logger,
    provideLoggerBackend(loggerBackend),
    provideLoggerId(gmeetId),
  ]);

  return injector.get(Transcriber) as Transcriber;
};
