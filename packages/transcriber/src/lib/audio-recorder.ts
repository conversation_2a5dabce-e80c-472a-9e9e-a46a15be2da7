import { Injectable } from 'injection-js';

import { Logger } from '@kurt/shared';

export const AUDIO_RECORDING_DATA_EVENT_NAME = 'audioRecordingData';

export interface AudioRecordingDataEventDetail {
  data: string;
  stream: unknown;
}

export const AUDIO_RECORDING_ENDED_EVENT_NAME = 'audioRecordingEnded';

export interface AudioRecordingEndedEventDetail {
  startedAt: Date;
  endedAt: Date;
}

@Injectable()
export class AudioRecorder extends EventTarget {
  private mediaRecorders: MediaRecorder[] = [];
  private startedAt?: Date;

  constructor(private logger: Logger) {
    super();
  }

  start() {
    const mediaStreams = Array.from(document.querySelectorAll('audio'))
      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call
      .map((element) => (element as any).captureStream() as MediaStream)
      .flatMap((audioStream) => audioStream.getAudioTracks())
      .map((audioTrack) => new MediaStream([audioTrack]));

    this.mediaRecorders = mediaStreams.map((mediaStream, index) => {
      const mediaRecorder = new MediaRecorder(mediaStream, {
        mimeType: 'audio/webm',
      });

      mediaRecorder.addEventListener(
        'dataavailable',
        this.onDataAvailable(index)
      );
      mediaRecorder.start(60 * 1000);

      return mediaRecorder;
    });
    this.startedAt = new Date();
  }

  async stop() {
    this.logger.log('Stopping audio recording');

    await Promise.all(
      this.mediaRecorders.map(
        (mediaRecorder) =>
          new Promise<void>((resolve) => {
            if (mediaRecorder.state === 'recording') {
              mediaRecorder.addEventListener(
                'stop',
                () => setTimeout(resolve, 100),
                {
                  once: true,
                }
              );

              mediaRecorder.stop();

              return;
            }
          })
      )
    );

    this.dispatchEvent(
      new CustomEvent<AudioRecordingEndedEventDetail>(
        AUDIO_RECORDING_ENDED_EVENT_NAME,
        {
          detail: {
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            startedAt: this.startedAt!,
            endedAt: new Date(),
          },
        }
      )
    );

    this.logger.log('Audio recording stopped');
  }

  private onDataAvailable =
    (index: number) =>
    (event: BlobEvent): void => {
      const fileReader = new FileReader();
      fileReader.addEventListener('loadend', () => {
        const data = fileReader.result as string;

        this.dispatchEvent(
          new CustomEvent<AudioRecordingDataEventDetail>(
            AUDIO_RECORDING_DATA_EVENT_NAME,
            { detail: { data, stream: index } }
          )
        );
      });

      fileReader.readAsBinaryString(event.data);
    };
}
