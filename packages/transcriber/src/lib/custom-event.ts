globalThis.CustomEvent = globalThis.CustomEvent
  ? globalThis.CustomEvent
  : class CustomEventPolyfill<T> extends Event {
      readonly detail: T;
      constructor(type: string, data?: EventInit & { detail?: T }) {
        super(type, data);
        this.detail = data?.detail as unknown as T;
      }
      /** @deprecated */
      initCustomEvent(
        _type: string,
        _bubbles?: boolean,
        _cancelable?: boolean,
        _detail?: T
      ) {
        throw new Error('deprecated');
      }
    };
