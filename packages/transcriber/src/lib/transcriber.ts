import { Inject, Injectable } from 'injection-js';

import {
  Logger,
  Participant,
  RECORD_CREATED_EVENT_NAME,
  RecordCreatedEventDetail,
  TASK_CREATED_EVENT_NAME,
  TaskCreatedEventDetail,
} from '@kurt/shared';

import {
  AUDIO_RECORDING_DATA_EVENT_NAME,
  AUDIO_RECORDING_ENDED_EVENT_NAME,
  AudioRecorder,
  AudioRecordingDataEventDetail,
  AudioRecordingEndedEventDetail,
} from './audio-recorder';
import {
  CAPTION_ADDED_EVENT_NAME,
  CAPTION_UPDATED_EVENT_NAME,
  CaptionAddedEventDetail,
  CaptionUpdatedEventDetail,
  CaptionsObserver,
} from './captions-observer';
import {
  CHAT_MESSAGE_POSTED_EVENT_NAME,
  ChatMessagePostedEventDetail,
  ChatObserver,
} from './chat-observer';
import { Database } from './database';
import { MEETING_DOCUMENT_ID } from './meeting-document-id';
import {
  PARTICIPANT_JOINED_EVENT_NAME,
  PARTICIPANT_LEFT_EVENT_NAME,
  ParticipantJoinedEventDetail,
  ParticipantLeftEventDetail,
  ParticipantsListObserver,
} from './participants-list-observer';
import {
  PresentationObserver,
  SCREENSHOT_CAPTURED_EVENT_NAME,
  ScreenshotCapturedEventDetail,
} from './presentation-observer';
import { SpeakerRecognizer } from './speaker-recognizer';

export const TRANSCRIPTION_STARTED_EVENT_NAME = 'transcriptionStarted';
export const TRANSCRIPTION_STOPPED_EVENT_NAME = 'transcriptionStopped';
export const PARTICIPANTS_LIST_CHANGED_EVENT_NAME = 'participantsListChanged';
export const NOTIFICATION_EVENT_NAME = 'notification';

export interface NotificationEventDetail {
  title: string;
  message: string;
}

export type ParticipantsListChangedEventDetail = Participant[];

@Injectable()
export class Transcriber extends EventTarget {
  private isTranscribing = false;
  private weTurnedCaptionsOn = false;

  constructor(
    private database: Database,
    private captionsObserver: CaptionsObserver,
    private participantsListObserver: ParticipantsListObserver,
    private presentationObserver: PresentationObserver,
    private chatObserver: ChatObserver,
    private audioRecorder: AudioRecorder,
    private speakerRecognizer: SpeakerRecognizer,
    @Inject(MEETING_DOCUMENT_ID) readonly meetingId: string,
    private logger: Logger
  ) {
    super();
    this.initializeParticipantsListObserver();
    this.initializeChatObserver();
    this.initializePresenationObserver();
    this.initializeCaptionsObserver();
    this.initializeAudioRecorder();
  }

  async initializeObservers() {
    this.enableCaptions();
    await this.chatObserver.observe();
    this.presentationObserver.observe();
    this.captionsObserver.observe();
    await this.participantsListObserver.observe();
    this.speakerRecognizer.observe();
  }

  toggleTranscription = () => {
    if (this.isTranscribing) {
      this.stopTranscription();

      return;
    }

    this.startTranscription();
  };

  startTranscription = async (language?: string) => {
    if (this.isTranscribing) {
      return;
    }

    try {
      await this.initializeObservers();
      if (language) {
        this.captionsObserver.setLanguage(language);
        this.chatObserver.setLanguage(language);
      }
      await this.initializeDatabase();
      window.addEventListener('beforeunload', this.unloadListener, {
        once: true,
      });

      this.isTranscribing = true;
      await this.database.setTranscriptionStarted();

      this.audioRecorder?.start();
      this.chatObserver?.sendChatMessage('Recording started.');
      this.dispatchEvent(new CustomEvent(TRANSCRIPTION_STARTED_EVENT_NAME));
    } catch (e) {
      this.logger.error(e as Error);

      return;
    }
  };

  stopTranscription = async (displayName?: string) => {
    if (!this.isTranscribing) {
      return;
    }

    this.isTranscribing = false;

    this.dispatchEvent(new CustomEvent(TRANSCRIPTION_STOPPED_EVENT_NAME));

    await this.audioRecorder?.stop();
    this.chatObserver?.sendChatMessage('Recording stopped.');
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    await this.database.setTranscriptionStopped(displayName);
    window.removeEventListener('beforeunload', this.unloadListener);

    if (!this.weTurnedCaptionsOn) {
      return;
    }

    this.disableCaptions();
  };

  disposeObservers() {
    this.captionsObserver?.disconnect();
    this.participantsListObserver?.disconnect();
    this.presentationObserver?.disconnect();
    this.chatObserver?.disconnect();
  }

  setLanguage(language: string) {
    this.captionsObserver?.setLanguage(language);
    this.chatObserver?.setLanguage(language);
  }

  sendMessage(message: string) {
    this.chatObserver?.sendChatMessage(message);
  }

  private unloadListener = async () => {
    if (this.isTranscribing) {
      await this.stopTranscription();
    }
  };

  private async initializeDatabase() {
    // const database = new Database(this.meetingId, this.databaseBackend);

    const meeting = await this.database.initializeMeeting();

    if (meeting.transcribedBy) {
      const message = `Meeting is already being recorded by ${meeting.transcribedBy}`;

      this.dispatchEvent(
        new CustomEvent<NotificationEventDetail>(NOTIFICATION_EVENT_NAME, {
          detail: {
            title: 'Can not record meeting.',
            message,
          },
        })
      );

      throw new Error(message);
    }
    // return database;
  }

  private initializePresenationObserver() {
    const presentationObserver = new PresentationObserver();

    this.bubbleEvent<ScreenshotCapturedEventDetail>(
      presentationObserver,
      SCREENSHOT_CAPTURED_EVENT_NAME,
      (data) => {
        this.database?.addScreenshot(data);

        return data;
      }
    );

    return presentationObserver;
  }

  private initializeParticipantsListObserver() {
    this.bubbleEvent<ParticipantJoinedEventDetail>(
      this.participantsListObserver,
      PARTICIPANT_JOINED_EVENT_NAME,
      (participant) => {
        this.logger.log(
          `${PARTICIPANT_JOINED_EVENT_NAME}: ${participant.name}`
        );
        this.database?.addEvent({
          type: 'PARTICIPANT_JOINED',
          data: participant.name,
          startedAt: new Date(),
        });
        this.dispatchEvent(
          new CustomEvent<ParticipantsListChangedEventDetail>(
            PARTICIPANTS_LIST_CHANGED_EVENT_NAME,
            { detail: this.participantsListObserver.participants }
          )
        );

        return participant;
      }
    );

    this.bubbleEvent<ParticipantLeftEventDetail>(
      this.participantsListObserver,
      PARTICIPANT_LEFT_EVENT_NAME,
      (participant) => {
        this.database?.addEvent({
          type: 'PARTICIPANT_LEFT',
          data: participant.name,
          startedAt: new Date(),
        });
        this.dispatchEvent(
          new CustomEvent<ParticipantsListChangedEventDetail>(
            PARTICIPANTS_LIST_CHANGED_EVENT_NAME,
            { detail: this.participantsListObserver.participants }
          )
        );

        return participant;
      }
    );
  }

  private initializeChatObserver() {
    this.bubbleEvent<ChatMessagePostedEventDetail>(
      this.chatObserver,
      CHAT_MESSAGE_POSTED_EVENT_NAME,
      (detail) => {
        const participant = this.getParticipant(detail.person);

        if (!participant) {
          this.logger.error(
            new Error(`Could not find participant [${detail.person}].`)
          );
        }

        const amendedDetail: ChatMessagePostedEventDetail = {
          ...detail,
          person: participant?.name || detail.person,
          image: participant?.avatar || detail.image,
        };

        this.database?.addMessage(amendedDetail);

        return amendedDetail;
      }
    );

    this.bubbleEvent<RecordCreatedEventDetail>(
      this.chatObserver,
      RECORD_CREATED_EVENT_NAME,
      (record) => this.handleRecordCreatedEvent(record)
    );

    this.bubbleEvent<RecordCreatedEventDetail>(
      this.chatObserver,
      TASK_CREATED_EVENT_NAME,
      (task) => this.handleTaskCreatedEvent(task)
    );
  }

  private initializeCaptionsObserver() {
    this.bubbleEvent<CaptionAddedEventDetail>(
      this.captionsObserver,
      CAPTION_ADDED_EVENT_NAME,
      (caption) => {
        const participant = this.getParticipant(caption.person);

        if (!participant) {
          this.logger.error(
            new Error(`Could not find participant [${caption.person}].`)
          );
        }

        const amendedCaption: CaptionAddedEventDetail = {
          ...caption,
          person: participant?.name || caption.person,
          image: participant?.avatar || caption.image,
        };

        const { id, ...transcript } = amendedCaption;

        this.database?.addTranscript(id, transcript);

        return amendedCaption;
      }
    );

    this.bubbleEvent<CaptionUpdatedEventDetail>(
      this.captionsObserver,
      CAPTION_UPDATED_EVENT_NAME,
      (caption) => {
        const participant = this.getParticipant(caption.person);

        if (!participant) {
          this.logger.error(
            new Error(`Could not find participant [${caption.person}].`)
          );
        }

        const amendedCaption: CaptionUpdatedEventDetail = {
          ...caption,
          person: participant?.name || caption.person,
          image: participant?.avatar || caption.image,
        };

        const { id, ...transcript } = amendedCaption;

        this.database?.addTranscript(id, transcript);

        return amendedCaption;
      }
    );

    this.bubbleEvent<TaskCreatedEventDetail>(
      this.captionsObserver,
      TASK_CREATED_EVENT_NAME,
      (task) => this.handleTaskCreatedEvent(task)
    );

    this.bubbleEvent<RecordCreatedEventDetail>(
      this.captionsObserver,
      RECORD_CREATED_EVENT_NAME,
      (record) => this.handleRecordCreatedEvent(record)
    );
  }

  private initializeAudioRecorder() {
    this.bubbleEvent<AudioRecordingDataEventDetail>(
      this.audioRecorder,
      AUDIO_RECORDING_DATA_EVENT_NAME
    );

    this.bubbleEvent<AudioRecordingEndedEventDetail>(
      this.audioRecorder,
      AUDIO_RECORDING_ENDED_EVENT_NAME
    );
  }

  private async enableCaptions() {
    const captionsButton =
      document.querySelector<HTMLButtonElement>('[jsname=r8qRAd]');

    if (!captionsButton) {
      this.logger.log('Captions button not found. Trying more options...');
      await this.enableCaptionsFromMoreOptions();
      return;
    }

    if (!captionsButton) {
      this.logger.error(new Error('Cannot find captions button.'));
      return;
    }

    if (
      captionsButton.getAttribute('aria-pressed') === 'true' ||
      this.weTurnedCaptionsOn
    ) {
      this.logger.log('Captions already enabled.');
      return;
    }

    captionsButton.click();
    this.weTurnedCaptionsOn = true;
  }

  private enableCaptionsFromMoreOptions() {
    return new Promise<void>((resolve) => {
      const menuButton = document.querySelector<HTMLButtonElement>(
        '[jsname="aGHX8e"] button'
      );

      menuButton?.click();

      requestAnimationFrame(() => {
        const captionsListItem =
          document.querySelector<HTMLLIElement>('[jsname="ARMOIc"]');

        captionsListItem?.click();

        setTimeout(() => {
          requestAnimationFrame(() => {
            const englishCaptionsRadio =
              document.querySelector<HTMLInputElement>(
                '[jsname="YPqjbf"][value="1"]'
              );

            englishCaptionsRadio?.click();

            const applyButton = document.querySelector<HTMLButtonElement>(
              '[data-mdc-dialog-action="OjYKac"]'
            );

            applyButton?.click();
            resolve();
          });
        }, 1000);
      });
    });
  }

  private disableCaptions() {
    const captionsButton = document.querySelector<HTMLButtonElement>(
      '[jscontroller="xzbRj"]'
    );

    if (!captionsButton) {
      return;
    }

    this.weTurnedCaptionsOn = false;

    if (captionsButton.getAttribute('aria-pressed') === 'false') {
      return;
    }

    captionsButton.click();
  }

  private bubbleEvent<T>(
    eventTarget: EventTarget,
    eventName: string,
    handler: (detail: T) => T = (detail) => detail
  ) {
    eventTarget.addEventListener(eventName, (event) => {
      const detail = handler((event as CustomEvent<T>).detail);

      this.dispatchEvent(new CustomEvent<T>(eventName, { detail }));
    });
  }

  private getParticipant(participantNameOrTitle: string) {
    const participantsList = this.participantsListObserver.participants;

    const participantFound = participantsList.find(
      (participant) =>
        participant.name === participantNameOrTitle ||
        participant.title?.replace(/^\((.*)\)$/, '$1') ===
          participantNameOrTitle
    );
    if (!participantFound) {
      participantsList.forEach((p) => {
        this.logger.log(
          `${participantNameOrTitle} -> ${p.name} : ${
            p.title ?? 'undefined'
          } : ${p.title?.replace(/^\((.*)\)$/, '$1') ?? 'undefined'}`
        );
      });
    }
    return participantFound;
  }

  private handleRecordCreatedEvent(record: RecordCreatedEventDetail) {
    const participant = this.getParticipant(record['creator-name']);

    if (!participant) {
      this.logger.error(
        new Error(`Could not find participant [${record['creator-name']}].`)
      );
    }

    const amendedRecord: RecordCreatedEventDetail = {
      ...record,
      'creator-name': participant?.name || record['creator-name'],
    };

    this.database?.addRecord({ ...amendedRecord, startedAt: new Date() });
    this.dispatchEvent(
      new CustomEvent<NotificationEventDetail>(NOTIFICATION_EVENT_NAME, {
        detail: { title: 'Record added.', message: record.title },
      })
    );
    this.chatObserver?.sendChatMessage(`Record added: ${record.title}`);

    return amendedRecord;
  }

  private handleTaskCreatedEvent(task: TaskCreatedEventDetail) {
    const participant = this.getParticipant(task['creator-name']);

    if (!participant) {
      this.logger.error(
        new Error(`Could not find participant [${task['creator-name']}].`)
      );
    }

    const amendedTask: TaskCreatedEventDetail = {
      ...task,
      'creator-name': participant?.name || task['creator-name'],
    };

    this.database?.addTask({ ...amendedTask, startedAt: new Date() });
    this.dispatchEvent(
      new CustomEvent<NotificationEventDetail>(NOTIFICATION_EVENT_NAME, {
        detail: { title: 'Task added.', message: task.title },
      })
    );
    this.chatObserver?.sendChatMessage(`Task added: ${task.title}`);

    return amendedTask;
  }
}
