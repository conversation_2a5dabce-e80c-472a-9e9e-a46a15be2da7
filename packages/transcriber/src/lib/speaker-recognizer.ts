import { Injectable } from 'injection-js';

import { Logger } from '@kurt/shared';

import { Database } from './database';

@Injectable()
export class SpeakerRecognizer extends EventTarget {
  private isActive = false;
  private readonly participantsListContainerSelector = '[jsname=ME4pNd]';
  private cachedParticipantsListItems: {
    person: string;
    isSpeaking: boolean;
    time: Date;
    isMuted: boolean;
  }[] = [];
  constructor(
    private logger: Logger,
    private database: Database,
  ) {
    super();
  }

  observe() {
    if (this.isActive) {
      return;
    }

    const participantsListContainer = document.querySelector(
      this.participantsListContainerSelector,
    );

    if (!participantsListContainer) {
      this.logger.error(new Error('Participants list container not found.'));

      return;
    }

    this.isActive = true;

    this.startSpeakerIdentification();
  }

  disconnect() {
    if (!this.isActive) {
      return;
    }

    this.isActive = false;
  }
  private startSpeakerIdentification() {
    try {
      this.logger.log('Querying list items...');
      //check if participants list is loaded
      const participantsListContainer = document.querySelector(
        this.participantsListContainerSelector,
      );
      if (!participantsListContainer) {
        this.logger.log('Participants list container not found.');
        return;
      }
      this.getWhoIsSpeaking(this.logger);
      this.logger.log('Querying list items... done');
    } catch (e) {
      this.logger.error(e as Error);
    }
    setInterval(() => {
      this.getWhoIsSpeaking(this.logger);
    }, 50);
  }

  private getWhoIsSpeaking(logger: Logger) {
    const participantsList =
      document
        .querySelector('[jsname=jrQDbd]')
        ?.querySelectorAll('[jsname="mu2b5d"]') ?? [];
    const participantsListItems: {
      person: string;
      isSpeaking: boolean;
      time: Date;
      isMuted: boolean;
    }[] = [];

    participantsList.forEach(async (child) => {
      const isPersonSpeaking =
        getComputedStyle(
          child.parentElement?.parentElement?.querySelector(
            '[jsname="QgSmzd"] > div:nth-child(2)',
          ) as HTMLElement,
        ).backgroundPositionX !== '0px';
      participantsListItems.push({
        person: child.querySelector('span')?.textContent ?? 'unknown',
        isSpeaking: isPersonSpeaking,
        time: new Date(),
        isMuted:
          (child.parentElement?.parentElement?.querySelector(
            '[jsname="QgSmzd"]',
          )?.children[0].clientHeight ?? 0) === 0,
      });
      // if (isPersonSpeaking) {
      //   logger.log(
      //     `Person ${
      //       child.querySelector('span')?.textContent ?? 'unknown'
      //     } is speaking`
      //   );
      // }
      if (this.cachedParticipantsListItems.length > 0) {
        const cachedPerson = this.cachedParticipantsListItems.find(
          (x) => x.person === child.querySelector('span')?.textContent,
        );
        if (cachedPerson && isPersonSpeaking !== cachedPerson.isSpeaking) {
          // logger.log(
          //   `Person ${
          //     child.querySelector('span')?.textContent ?? 'unknown'
          //   } changed speaking state to ${
          //     isPersonSpeaking ? 'speaking' : 'not speaking'
          //   } at ${new Date().toLocaleString()}`
          // );
          this.cachedParticipantsListItems =
            this.cachedParticipantsListItems.filter(
              (x) => x.person !== child.querySelector('span')?.textContent,
            );
          const personData = {
            person: child.querySelector('span')?.textContent ?? 'unknown',
            isSpeaking: isPersonSpeaking,
            time: new Date(),
            isMuted:
              (child.parentElement?.parentElement?.querySelector(
                '[jsname="QgSmzd"]',
              )?.children[0].clientHeight ?? 0) === 0,
          };
          await this.database.addMeetingSpeakers({
            name: personData.person,
            isSpeaking: personData.isSpeaking,
            date: new Date(personData.time),
            isMuted: personData.isMuted,
          });
          this.cachedParticipantsListItems.push(personData);
        } else if (!cachedPerson) {
          const personData = {
            person: child.querySelector('span')?.textContent ?? 'unknown',
            isSpeaking: isPersonSpeaking,
            time: new Date(),
            isMuted:
              (child.parentElement?.parentElement?.querySelector(
                '[jsname="QgSmzd"]',
              )?.children[0].clientHeight ?? 0) === 0,
          };
          await this.database.addMeetingSpeakers({
            name: personData.person,
            isSpeaking: personData.isSpeaking,
            date: new Date(personData.time),
            isMuted: personData.isMuted,
          });
          this.cachedParticipantsListItems.push(personData);
        }
      } else {
        logger.log(`Cached list is empty`);
        this.cachedParticipantsListItems = participantsListItems;
        participantsListItems.forEach(async (personData) => {
          await this.database.addMeetingSpeakers({
            name: personData.person,
            isSpeaking: personData.isSpeaking,
            date: new Date(personData.time),
            isMuted:
              (child.parentElement?.parentElement?.querySelector(
                '[jsname="QgSmzd"]',
              )?.children[0].clientHeight ?? 0) === 0,
          });
        });
      }
    });
  }
}
