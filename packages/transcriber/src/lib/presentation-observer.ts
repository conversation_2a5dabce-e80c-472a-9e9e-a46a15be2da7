// .PoIECb videoElement.closest('.xsj2Ff')

import { Injectable } from 'injection-js';

export const SCREENSHOT_CAPTURED_EVENT_NAME = 'screenshotCaptured';

export type ScreenshotCapturedEventDetail = string;

@Injectable()
export class PresentationObserver extends EventTarget {
  // private presentationObserver = new MutationObserver((_mutations) => {});
  private canvas = document.createElement('canvas');
  private canvasContext = this.canvas.getContext('2d');
  private mainContainerObserver = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (!mutation.addedNodes.length) {
        return;
      }

      mutation.addedNodes.forEach((addedNode) => {
        if (!(addedNode instanceof HTMLVideoElement)) {
          return;
        }

        this.addScreenshotButton(addedNode);
      });
    });
  });
  private isActive = false;

  observe() {
    if (this.isActive) {
      return;
    }

    this.isActive = true;

    const videoElements = document.querySelectorAll('video');

    videoElements.forEach(this.addScreenshotButton);

    this.mainContainerObserver.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }

  disconnect() {
    if (!this.isActive) {
      return;
    }

    this.isActive = false;
    this.mainContainerObserver.disconnect();
    // this.presentationObserver.disconnect();
  }

  private addScreenshotButton = (videoElement: HTMLVideoElement) => {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const parentElement = videoElement.parentElement!;
    const div = document.createElement('div');

    div.className = 'rG0ybd xPh1xb P9KVBf';

    // button.type = 'button';
    div.setAttribute('aria-label', 'screenshot');
    div.innerHTML = `
    <button type="button" class="VfPpkd-Bz112c-LgbsSe yHy1rc eT1oJ tWDL4c uaILN" aria-label="Screenshot">
      <div class="VfPpkd-Bz112c-Jh9lGc"></div>
      <i class="google-material-icons" aria-hidden="true">screenshot</i>
    </button>
    `;
    div.style.position = 'absolute';
    div.style.top = '0.25rem';
    div.style.left = '0.25rem';
    div.style.zIndex = '1000';
    div.style.width = 'auto';
    div.style.alignItems = 'start';
    div.style.backgroundColor = 'transparent';
    // button.style.borderRadius = '999999px';
    // button.style.fontSize = '1rem';
    // button.style.padding = '0.25rem';
    // button.style.height = '1.5rem';
    // button.style.width = '1.5rem';
    // div.className = '';
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    div
      .querySelector('button')!
      .addEventListener('click', () => this.saveScreenshot(videoElement));
    parentElement.appendChild(div);
    parentElement.style.position = 'relative';
  };

  private saveScreenshot(element: HTMLVideoElement) {
    this.canvas.width = element.videoWidth / 2;
    this.canvas.height = element.videoHeight / 2;

    this.canvasContext?.drawImage(
      element,
      0,
      0,
      this.canvas.width,
      this.canvas.height
    );

    const imageDataUrl = this.canvas.toDataURL('image/jpg', 0.7);
    const data = imageDataUrl.split(',')[1];

    this.dispatchEvent(
      new CustomEvent<ScreenshotCapturedEventDetail>(
        SCREENSHOT_CAPTURED_EVENT_NAME,
        { detail: data }
      )
    );
  }
}
