export class VoiceCommandMatcher {
  private hotWordRegex: RegExp;
  private hotWordRemovalRegex: RegExp;
  private recordsCommandRegex: RegExp;
  private recordsCommandRemovalRegex: RegExp;

  constructor(hotWord: string, recordsWord: string[]) {
    const hotWordCaptureGroups = hotWord
      .split(/\s/g)
      .map((part) => part.replace(/\W+/g, ' '))
      .join('\\W+');

    this.hotWordRegex = new RegExp(`(?:${hotWordCaptureGroups})`, 'i');
    this.hotWordRemovalRegex = new RegExp(
      `.*${this.hotWordRegex.source}\\W*`,
      'i'
    );
    this.recordsCommandRegex = new RegExp(
      `^(?:(?:\\S+\\s){0,4})(?:${recordsWord.join('|')})`,
      'i'
    );
    this.recordsCommandRemovalRegex = new RegExp(
      `.*${this.recordsCommandRegex.source}\\W*`,
      'i'
    );
  }

  isCommand(text: string) {
    return this.hotWordRegex.test(text);
  }

  trimHotWord(text: string) {
    return this.capitalizeFirstCharacter(
      text.split(this.hotWordRemovalRegex)[1].trim()
    );
  }

  isRecord(text: string) {
    return this.recordsCommandRegex.test(text);
  }

  trimRecordHotWord(text: string) {
    return this.capitalizeFirstCharacter(
      text.split(this.recordsCommandRemovalRegex)[1].trim()
    );
  }

  private capitalizeFirstCharacter(text: string) {
    return `${text[0].toUpperCase()}${text.slice(1)}`;
  }
}
