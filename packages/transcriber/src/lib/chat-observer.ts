import { Injectable } from 'injection-js';

import {
  Logger,
  RECORD_CREATED_EVENT_NAME,
  RecordCreatedEventDetail,
  TASK_CREATED_EVENT_NAME,
  TaskCreatedEventDetail,
} from '@kurt/shared';

export const CHAT_MESSAGE_POSTED_EVENT_NAME = 'chatMessagePosted';

export interface ChatMessagePostedEventDetail {
  text: string;
  image: string;
  person: string;
  endedAt: Date;
  startedAt: Date;
}

const taskCommandRegex = /^task:/gi;
const recordCommandRegex = /^ftr:/gi;

@Injectable()
export class ChatObserver extends EventTarget {
  private messageInput?: HTMLTextAreaElement;
  private sendMessageButton?: HTMLButtonElement;
  private childListObserver = new MutationObserver((mutationRecords) => {
    const relevantMutations = mutationRecords.filter(
      (mutation) =>
        mutation.target instanceof HTMLDivElement && mutation.addedNodes.length,
    );

    for (const mutation of relevantMutations) {
      this.logger.log(
        `Chat Mutation observed. ${mutation?.target?.textContent ?? ''}`,
      );

      const target = mutation.target as HTMLElement;
      this.logger.log(
        `Chat mutation classes: ${target?.classList} -> ${target.innerHTML}`,
      );

      // new message added to existing thread
      if (target.classList.contains('beTDc')) {
        this.logger.log(
          `New message added to existing thread. added nodes: ${mutation.addedNodes.length}`,
        );
        mutation.addedNodes.forEach((addedNode) => {
          if (!(addedNode instanceof HTMLDivElement)) {
            return;
          }
          this.logger.log('Chat reply Added node. ' + addedNode?.innerHTML);

          // pending message
          if (addedNode.classList.contains('gYckH')) {
            this.logger.log('Pending message.');
            return;
          }

          this.addMessage(addedNode as HTMLElement, true);
        });

        return;
      } else {
        this.logger.log('Chat Mutation not contains. beTDc');
      }

      // new thread
      // if (target.classList.contains('z38b6')) {
      //   this.logger.log('Chat New thread.');
      //   mutation.addedNodes.forEach((addedNode) => {
      //     this.logger.log(`Chat Added node.${addedNode?.textContent ?? "-"} ${addedNode?.nodeName}`);
      //     if (!(addedNode instanceof HTMLElement)) {
      //       this.logger.log('Chat Added node is not an HTMLElement.');
      //       return;
      //     }
      //
      //     //log the html content of the added node
      //     this.logger.log('Chat Added node html: ' + addedNode.innerHTML);
      //     this.addMessage(addedNode, false);
      //
      //     return;
      //
      //   });
      // } else {
      //   this.logger.log('Chat Mutation not contains. z38b6'  );
      // }
    }
  });

  private readonly messageContainerSelector = '[jsname="xySENc"]';
  private readonly messageThreadSelector = `${this.messageContainerSelector} > [jsname="dTKtvb"]`;
  private readonly pluginChatMessagesPrefix = '[KURT]';
  private language?: string;
  private isActive = false;
  private lastParticipantName?: string;

  constructor(private logger: Logger) {
    super();
  }

  async observe() {
    this.logger.log('Observing chat called.');
    if (this.isActive) {
      return;
    }

    this.isActive = true;
    this.logger.log('loading chat.');
    await this.loadChat();

    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    this.messageInput = document.getElementById('bfTqV') as HTMLTextAreaElement;
    this.sendMessageButton =
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      document.querySelector<HTMLButtonElement>('[jsname="SoqoBf"]')!;

    const messagesContainer = document.querySelector(
      this.messageContainerSelector,
    );

    if (!messagesContainer) {
      this.logger.error(new Error('Messages container not found.'));
      this.isActive = false;

      return;
    }

    setTimeout(() => {
      this.childListObserver.observe(messagesContainer, {
        childList: true,
        subtree: true,
      });
    }, 500);
  }

  disconnect() {
    if (!this.isActive) {
      return;
    }

    this.isActive = false;
    this.childListObserver.disconnect();
  }

  sendChatMessage(message: string) {
    if (!this.messageInput) {
      return;
    }

    this.messageInput.value = `${this.pluginChatMessagesPrefix} ${message}`;
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    this.sendMessageButton!.disabled = false;
    this.sendMessageButton?.click();
  }

  setLanguage(language: string) {
    this.language = language;
  }

  private loadChat() {
    return new Promise<void>((resolve, reject) => {
      const maxAttempts = 20; // sets the maximum number of attempts
      let attempts = 0; // starts the count at 0

      const checkButtonsExist = setInterval(() => {
        attempts++;
        this.logger.log(`Checking buttons exist. ${attempts}`);
        const buttonsContainer = document.querySelectorAll(
          '[jscontroller=rYZP8b]',
        );

        this.logger.log(`Buttons container: ${buttonsContainer?.length}`);
        if (buttonsContainer && buttonsContainer.length >= 3) {
          const chatButton = buttonsContainer
            .item(2)
            .querySelector('button') as HTMLButtonElement;
          if (chatButton) {
            this.logger.log('Chat button found.');
            chatButton.click();
            setTimeout(() => {
              chatButton.click();
              this.logger.log('Chat opened.');
              resolve();
            }, 500);
          } else {
            this.logger.error(new Error('Cannot find chat button.'));
            reject(new Error('Cannot find chat button.'));
          }
          clearInterval(checkButtonsExist); // stops the interval check
        } else if (attempts === maxAttempts) {
          this.logger.error(new Error("Cannot find buttons' container."));
          reject(new Error("Cannot find buttons' container."));
          clearInterval(checkButtonsExist); // stops the interval if max attempts is reached
        } else {
          this.logger.log(`Buttons not found. ${attempts}`);
        }
      }, 500); // check every 500 milliseconds
    });
  }

  private addMessage = (messageElement: HTMLElement, isReply: boolean) => {
    this.logger.log('Adding message. ' + messageElement.innerHTML);

    //find by jsname="dTKtvb"
    const messagesThreadElement = messageElement.querySelector(
      '[jsname="dTKtvb"]',
    ) as HTMLElement | undefined;

    if (!messagesThreadElement) {
      this.logger.error(new Error('Could not find root for message.'));

      return;
    }

    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const text = messagesThreadElement.textContent!;

    if (text.startsWith(this.pluginChatMessagesPrefix)) {
      this.logger.log('Message is plugin message.');
      return;
    }

    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const participantName =
      isReply && this.lastParticipantName
        ? this.lastParticipantName
        : (messageElement.querySelector('.poVWob')?.textContent ??
          // find the first ancestor with class Ss4fHf and then the first element with class name "poVWob" and get the text content
          messageElement.closest('.Ss4fHf')?.querySelector('.poVWob')
            ?.textContent ??
          'Unknown');

    this.lastParticipantName = participantName;

    if (taskCommandRegex.test(text)) {
      this.logger.log('Task command detected.');
      const task = text.replace(taskCommandRegex, '').trim();

      this.dispatchEvent(
        new CustomEvent<TaskCreatedEventDetail>(TASK_CREATED_EVENT_NAME, {
          detail: {
            'creator-name': participantName,
            title: `${task[0].toUpperCase()}${task.slice(1)}`,
            language: this.language,
          },
        }),
      );

      return;
    }

    if (recordCommandRegex.test(text)) {
      this.logger.log('Record command detected.');
      const record = text.replace(recordCommandRegex, '').trim();

      this.dispatchEvent(
        new CustomEvent<RecordCreatedEventDetail>(RECORD_CREATED_EVENT_NAME, {
          detail: {
            'creator-name': participantName,
            title: `${record[0].toUpperCase()}${record.slice(1)}`,
            language: this.language,
          },
        }),
      );

      return;
    }

    this.logger.log('Chat message posted.' + text);
    this.dispatchEvent(
      new CustomEvent<ChatMessagePostedEventDetail>(
        CHAT_MESSAGE_POSTED_EVENT_NAME,
        {
          detail: {
            text,
            image: '',
            person: participantName,
            endedAt: new Date(),
            startedAt: new Date(),
          },
        },
      ),
    );
  };
}
