import { Inject, Injectable } from 'injection-js';

import {
  DatabaseBackend,
  DATABASE_BACKEND,
  ExtensionEvent,
  Logger,
  Meeting,
  MeetingCollection,
  SpeakerInfo,
  TaskOrRecord,
  Transcript
} from '@kurt/shared';

import { MEETING_DOCUMENT_ID } from './meeting-document-id';

@Injectable()
export class Database {
  constructor(
    @Inject(MEETING_DOCUMENT_ID) private meetingId: string,
    @Inject(DATABASE_BACKEND) private backend: DatabaseBackend,
    private logger: Logger
  ) {
    this.backend.initialize(this.meetingId);
  }

  async initializeMeeting() {
    const meeting = await this.backend.getMeeting();
    const email = await this.backend.getCurrentUserEmail();
    const name = await this.backend.getCurrentUserDisplayName();

    if (meeting) {
      const meetingUpdates: Partial<Meeting> = {};

      if (!meeting['accessed-by']?.includes(email)) {
        meetingUpdates['accessed-by'] = [
          ...(meeting['accessed-by'] || []),
          email,
        ];
      }

      if (!(meeting['organizer-email'] || meeting['organizer-name'])) {
        meetingUpdates['organizer-email'] = email;
        meetingUpdates['organizer-name'] = name;
      }

      if (!meeting.botJoined) {
        meetingUpdates.botJoined = new Date();
      }

      if (!Object.keys(meetingUpdates).length) {
        return meeting;
      }
      this.logger.log('Updating meeting info.');
      return await this.backend.updateMeeting(meetingUpdates);
    }

    const newMeeting: Meeting = {
      id: this.meetingId,
      date: new Date(),
      title: this.meetingId,
      summary: '',
      'accessed-by': [email],
      'organizer-email': email,
      'organizer-name': name,
      botJoined: new Date(),
    };

    return await this.backend.createMeeting(newMeeting);
  }

  async addTranscript(id: string, transcript: Transcript) {
    this.logger.log('Adding transcript: ' + transcript.text);
    await this.tryPromise(
      Promise.all([
        this.backend.updateMeeting({ lastRecording: new Date() }),
        this.backend.createDocument(MeetingCollection.Transcripts, id, {
          ...transcript,
        }),
      ]),
      'Transcript added.'
    );
  }

  async updateTranscript({
    id,
    ...transcript
  }: {
    id: string;
    text: string;
    endedAt: Date;
  }) {
    await this.tryPromise(
      Promise.all([
        this.backend.updateMeeting({ lastRecording: new Date() }),
        this.backend.updateDocument(
          MeetingCollection.Transcripts,
          id,
          transcript
        ),
      ]),
      'Transcript updated.'
    );
  }

  async addTask(task: TaskOrRecord) {
    await this.tryPromise(
      this.backend.createDocument(
        MeetingCollection.Tasks,
        `${task['creator-name']}-${task.startedAt.valueOf()}`,
        task
      ),
      'Task added.'
    );
  }

  async addRecord(record: TaskOrRecord) {
    await this.tryPromise(
      this.backend.createDocument(
        MeetingCollection.Records,
        `${record['creator-name']}-${record.startedAt.valueOf()}`,
        record
      ),
      'Record added.'
    );
  }

  // async updateTask({
  //   document,
  //   title,
  // }: {
  //   document: DocumentReference;
  //   title: string;
  // }) {
  //   updateDoc(document, { title, startedAt: new Date() })
  //     .then(() => {
  //       logger.info('Task updated.');
  //     })
  //     .catch((err) => logger.debug(err));
  // }

  async addEvent(event: ExtensionEvent) {
    await this.tryPromise(
      this.backend.createDocument(
        MeetingCollection.Events,
        `${event.data}-${event.startedAt.valueOf()}`,
        event
      ),
      'Event added.'
    );
  }

  async setTranscriptionStarted() {
    const email = await this.backend.getCurrentUserDisplayName();

    await this.tryPromise(
      Promise.all([
        this.addEvent({
          type: 'TRANSCRIPTION_STARTED',
          data: email,
          startedAt: new Date(),
        }),
        this.backend.updateMeeting({
          transcribedBy: email,
        }),
        this.backend.updateMeetingLiveInfo({
          isLive: true,
          status: 'Recording',
        }),
      ])
    );
  }

  async setTranscriptionStopped(displayName = 'Airbus Assistant') {
    await this.tryPromise(
      Promise.all([
        this.addEvent({
          type: 'TRANSCRIPTION_STOPPED',
          data: displayName,
          startedAt: new Date(),
        }),
        this.backend.updateMeeting({
          transcribedBy: null,
        }),
        this.backend.updateMeetingLiveInfo({
          isLive: false,
          status: 'Stopping',
        }),
      ])
    );
  }

  async addScreenshot(data: string) {
    const now = new Date();
    const email = await this.backend.getCurrentUserEmail();

    await this.tryPromise(
      this.backend.createDocument(
        MeetingCollection.Screenshots,
        `${email}-${now.valueOf()}`,
        {
          data,
          startedAt: now,
          person: email,
          isSaved: false,
        }
      ),
      'Screenshot added.'
    );
  }

  async addMessage(data: Transcript) {
    await this.tryPromise(
      this.backend.createDocument(
        MeetingCollection.Messages,
        `${data.person}-${data.startedAt.valueOf()}`,
        data
      ),
      'Message added.'
    );
  }

  async addMeetingSpeakers(speakers: SpeakerInfo) {
    await this.tryPromise(
      this.backend.addSpeakerInfo(speakers),
      'Meeting speakers updated.'
    );
  }

  private async tryPromise(promise: Promise<unknown>, successMessage?: string) {
    try {
      await promise;

      if (successMessage) {
        this.logger.log(successMessage);
      }
    } catch (e) {
      this.logger.error(e as Error);
    }
  }
}
