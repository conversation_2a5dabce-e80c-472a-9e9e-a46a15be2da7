import { Injectable } from 'injection-js';

import { Logger, Participant } from '@kurt/shared';

export const PARTICIPANT_JOINED_EVENT_NAME = 'participantJoined';
export const PARTICIPANT_LEFT_EVENT_NAME = 'participantLeft';

export type ParticipantJoinedEventDetail = Participant;
export type ParticipantLeftEventDetail = Participant;

@Injectable()
export class ParticipantsListObserver extends EventTarget {
  get participants() {
    return this._participants;
  }

  private _participants: Participant[] = [];
  private participantsToAdd: Participant[] = [];
  private participantsToRemove: Participant[] = [];
  private processChangesTimeout?: ReturnType<typeof setTimeout>;
  private participantsButton!: HTMLButtonElement;
  private participantsListContainer!: Element;
  private participantsListContainerObserver = new MutationObserver(
    (mutations) => {
      this.logger.log('Participants list loaded.');
      const participantsList = mutations.find(
        (mutation) =>
          !!(mutation.target as HTMLElement).querySelector('[jsname=jrQDbd]')
      )?.target as HTMLElement | undefined;

      // this.logger.log(`Part List Text content: ${participantsList?.outerHTML ?? "undefined"}`);

      if (!participantsList) {
        this.logger.log('Participants list not found. Potential alternatives:');

        for (const mutation of mutations) {
          if (mutation.target instanceof HTMLElement) {
            const classNameSelector = `.${mutation.target.className
              .split(' ')
              .join('.')}`;
            const attributeSelector = Array.from(mutation.target.attributes)
              .map((attribute) => `[${attribute.name}=${attribute.value}]`)
              .join('');
            const hasListItems =
              mutation.target.querySelectorAll('[role=listitem]').length > 0;

            this.logger.log(`${classNameSelector}${attributeSelector}
            hasListItemsAsChildren: ${
              // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
              hasListItems
            }`);
          }
        }

        return;
      }

      try {
        this.logger.log('Querying list items...');
        const participantsListItems =
          participantsList.querySelectorAll('[role=listitem]');
        this.logger.log(`List items: ${participantsListItems.length}`);
        const currentParticipants = Array.from(participantsListItems)
          .map(this.mapListItemToParticipant)
          .filter((a): a is Participant => !!a);

        this.logger.log(`Initial participants: ${currentParticipants.length}
      ${currentParticipants
        .map((participant) => JSON.stringify(participant, null, 2))
        .join('\n')}
      `);

        currentParticipants.forEach(this.addParticipant);
      } catch (e) {
        this.logger.error(e as Error);
      }
      this.participantsListObserver.observe(participantsList, {
        childList: true,
        subtree: true,
      });
      this.participantsListContainerObserver.disconnect();
    }
  );

  private participantsListObserver = new MutationObserver((mutations) => {
    this.logger.log(
      `Participants list changed. Mutations: ${mutations.length}`
    );

    mutations.forEach((mutation, index) => {
      this.logger.log(
        `Mutation #${index} addedNodes: ${mutation.addedNodes.length} removedNodes: ${mutation.removedNodes.length}`
      );

      Array.from(mutation.addedNodes)
        .filter(
          (addedNode): addedNode is HTMLElement =>
            addedNode instanceof HTMLElement &&
            addedNode.getAttribute('role') === 'listitem'
        )
        .forEach((addedNode) => {
          const participant = this.mapListItemToParticipant(addedNode);
          if (participant) {
            this.participantsToAdd.push(participant);
          }
        });

      Array.from(mutation.removedNodes)
        .filter(
          (removedNode): removedNode is HTMLElement =>
            removedNode instanceof HTMLElement &&
            removedNode.getAttribute('role') === 'listitem'
        )
        .forEach((removedNode) => {
          const participant = this.mapListItemToParticipant(removedNode);
          if (participant) {
            this.participantsToRemove.push(participant);
          }
        });

      if (this.processChangesTimeout) {
        clearTimeout(this.processChangesTimeout);
      }

      setTimeout(() => {
        this.logger.log('Processing changes to participants list.');
        const dedupedParticipantsToAdd = this.dedupeParticipants(
          this.participantsToAdd
        );
        const dedupedParticipantsToRemove = this.dedupeParticipants(
          this.participantsToRemove
        );
        const participantsToIgnore = dedupedParticipantsToAdd.filter(
          (participantToAdd) =>
            dedupedParticipantsToRemove.some((participantToRemove) =>
              this.isSameParticipant(participantToAdd, participantToRemove)
            )
        );

        for (const participantToAdd of dedupedParticipantsToAdd) {
          if (
            participantsToIgnore.some((participant) =>
              this.isSameParticipant(participantToAdd, participant)
            )
          ) {
            continue;
          }

          this.logger.log(
            `Adding participant ${JSON.stringify(participantToAdd, null, 2)}`
          );
          this.addParticipant(participantToAdd);
        }

        for (const participantToRemove of dedupedParticipantsToRemove) {
          if (
            participantsToIgnore.some((participant) =>
              this.isSameParticipant(participantToRemove, participant)
            )
          ) {
            continue;
          }

          this.logger.log(
            `Removing participant ${JSON.stringify(
              participantToRemove,
              null,
              2
            )}`
          );
          this.removeParticipant(participantToRemove);
        }

        this.participantsToAdd = [];
        this.participantsToRemove = [];
      }, 5000);
    });
  });
  private isActive = false;
  private readonly participantsListContainerSelector = '[jsname=ME4pNd]';

  constructor(private logger: Logger) {
    super();
  }

  async observe() {
    if (this.isActive) {
      return;
    }

    const participantsListContainer = document.querySelector(
      this.participantsListContainerSelector
    );

    if (!participantsListContainer) {
      this.logger.error(new Error('Participants list container not found.'));

      return;
    }

    this.isActive = true;
    this.participantsListContainerObserver.observe(participantsListContainer, {
      childList: true,
    });

    //wait for 5 seconds
    await this.wait(5000);

    await this.loadParticipants();
  }

  disconnect() {
    if (!this.isActive) {
      return;
    }

    this.isActive = false;
    this.participantsListObserver.disconnect();
    this._participants = [];
  }

  private async loadParticipants() {
    const buttonsContainer = document.querySelectorAll('[jscontroller=rYZP8b]');

    this.logger.log('Opening participants list - god help us');

    if (!buttonsContainer) {
      this.logger.error(new Error(`Cannot find buttons' container.`));
      return;
    }

    this.participantsButton = buttonsContainer
      .item(1)
      .querySelector('button') as HTMLButtonElement;

    if (!this.participantsButton) {
      this.logger.error(new Error('Cannot find participants button.'));

      return;
    }

    this.logger.log(
      `Second button (participants??) ${
        this.participantsButton.textContent ?? 'undefined???'
      }`
    );

    this.logger.log('Clicking participants list button');

    this.participantsListContainer = document.querySelector(
      this.participantsListContainerSelector
    ) as Element;

    await this.toggleParticipantsListContainer(() =>
      this.participantsListContainer.classList.contains('qdulke')
    );
  }

  private mapListItemToParticipant = (
    participant: Element
  ): Participant | undefined => {
    try {
      const participantDetails = participant.querySelector(
        'div > div:nth-child(2)'
      );

      if (
        participantDetails &&
        participantDetails.querySelector('div > span:first-child')
          ?.textContent !== null
      ) {
        return {
          avatar: participant.querySelector('img')?.src ?? '',
          name:
            participantDetails.querySelector('div > span:first-child')
              ?.textContent ?? '-',
          title:
            participantDetails.querySelector('div > span:nth-child(2)')
              ?.textContent || undefined,
          subtitle:
            participantDetails.querySelector('div:nth-child(2)')?.textContent ||
            undefined,
        };
      } else {
        this.logger.error(new Error('Element was null'));
      }
    } catch (e) {
      this.logger.error(e as Error);
    }
    return undefined;
  };

  private addParticipant = (participantToAdd: Participant) => {
    const isDuplicate =
      this._participants.filter(
        (participant) => participant.avatar === participantToAdd.avatar
      ).length > 0;

    this._participants = [...this._participants, participantToAdd];

    if (isDuplicate) {
      return;
    }

    this.dispatchEvent(
      new CustomEvent<ParticipantJoinedEventDetail>(
        PARTICIPANT_JOINED_EVENT_NAME,
        { detail: participantToAdd }
      )
    );
  };

  private removeParticipant = (participantToRemove: Participant) => {
    this._participants = this._participants.filter(
      (participant) => !this.isSameParticipant(participant, participantToRemove)
    );

    if (
      this._participants.some((p) => p.avatar === participantToRemove.avatar)
    ) {
      return;
    }

    this.dispatchEvent(
      new CustomEvent<ParticipantLeftEventDetail>(PARTICIPANT_LEFT_EVENT_NAME, {
        detail: participantToRemove,
      })
    );
  };

  private isSameParticipant(
    participant1: Participant,
    participant2: Participant
  ) {
    return (
      participant1.avatar === participant2.avatar &&
      participant1.name === participant2.name &&
      participant1.subtitle === participant2.subtitle &&
      participant1.title === participant2.title
    );
  }

  private dedupeParticipants(participants: Participant[]) {
    return participants.reduce<Participant[]>((result, maybeDupe) => {
      if (
        !result.some((deduped) => this.isSameParticipant(deduped, maybeDupe))
      ) {
        result.push(maybeDupe);
      }

      return result;
    }, []);
  }

  private async toggleParticipantsListContainer(condition: () => boolean) {
    if (condition()) {
      await this.wait(1000);
      this.participantsButton.click();
    }
  }

  private wait(timeoutMs: number = 500) {
    return new Promise<void>((resolve) => {
      setTimeout(resolve, timeoutMs);
    });
  }
}
