import { Injectable } from 'injection-js';

import {
  Logger,
  RECORD_CREATED_EVENT_NAME,
  RecordCreatedEventDetail,
  TASK_CREATED_EVENT_NAME,
  TaskCreatedEventDetail,
  Transcript,
} from '@kurt/shared';

import { VoiceCommandMatcher } from './voice-command-matcher';

interface TasksCacheEntry {
  timeoutId?: ReturnType<typeof setTimeout>;
  elements: Set<HTMLElement>;
}

export const CAPTION_ADDED_EVENT_NAME = 'captionAdded';
export const CAPTION_UPDATED_EVENT_NAME = 'captionUpdated';

export interface CaptionAddedEventDetail {
  id: string;
  text: string;
  person: string;
  image: string;
  startedAt: Date;
  endedAt: Date;
  language?: string;
}

export interface CaptionUpdatedEventDetail {
  id: string;
  text: string;
  person: string;
  image: string;
  startedAt: Date;
  endedAt: Date;
}

@Injectable()
export class CaptionsObserver extends EventTarget {
  private captionsContainerSelector = '[jsname=dsyhDe].iOzk7 > div';
  // private captionsThreadSelector = `${this.captionsContainerSelector} > *`;
  private captionsDebounceMap = new WeakMap<
    HTMLElement,
    ReturnType<typeof setTimeout>
  >();
  private transcriptsCache = new WeakMap<
    HTMLElement,
    { id: string; transcript: Transcript }
  >();

  private childListObserver = new MutationObserver((mutationRecords) => {
    this.logger.log(
      // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
      'childListObserver Mutation observed with class names: ' +
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return
        mutationRecords.map(
          (mutation) => (mutation.target as HTMLElement).className
        )
    );
    const relevantMutations = mutationRecords.filter(
      (mutation) =>
        mutation.target instanceof HTMLDivElement &&
        mutation.target.className.includes('nMcdL')
    );

    // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
    this.logger.log('relevantMutations: ' + relevantMutations.length);

    relevantMutations.forEach((mutation) => {
      const captionElement = mutation.target as HTMLElement;
      this.addCaption(captionElement);
    });
  });

  private tasksCache = new Map<string, TasksCacheEntry>();
  private language?: string;
  private isActive = false;
  private voiceCommandMatchersMap = new Map([
    ['en', new VoiceCommandMatcher('Hey assistant', ['records?'])],
    ['de', new VoiceCommandMatcher('Hey assistent', ['Aufzeichnung(en)?'])],
    ['fr', new VoiceCommandMatcher('Salut assistante', ['dossiers?'])],
    ['es-es', new VoiceCommandMatcher('Hola asistente', ['conste'])],
    ['pt-br', new VoiceCommandMatcher('Oi assistente', ['registros?'])],
  ]);
  private readonly voiceCommandTimeout = 3000;

  constructor(private logger: Logger) {
    super();
  }

  observe() {
    this.logger.log('Observing captions.');

    const captionsContainer = document.querySelector(
      this.captionsContainerSelector
    ) as HTMLElement;

    //log the captions container
    this.logger.log('captionsContainer: ' + captionsContainer.id);

    if (!captionsContainer) {
      this.logger.error(new Error('Captions container not found.'));

      return;
    }

    this.isActive = true;
    this.logger.log('Captions observer is active.');
    this.childListObserver.observe(captionsContainer, {
      characterData: true,
      attributes: true,
      childList: true,
      subtree: true,
    });
  }

  disconnect() {
    if (!this.isActive) {
      return;
    }

    this.isActive = false;
    this.childListObserver.disconnect();
  }

  setLanguage(language: string) {
    this.language = language;
  }

  private get voiceCommandMatcher() {
    if (!this.language || !this.voiceCommandMatchersMap.has(this.language)) {
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      return this.voiceCommandMatchersMap.get('en')!;
    }

    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    return this.voiceCommandMatchersMap.get(this.language)!;
  }

  private addCaption = (captionElement: HTMLElement) => {
    this.logger.log(
      'captionElement: ' +
        captionElement.id +
        'classnames' +
        captionElement.className
    );

    const captionsThreadElement = captionElement;

    if (!captionsThreadElement) {
      this.logger.error(new Error('Could not find root for caption.'));
      return;
    }
    const textElement = captionElement.querySelector('.bh44bd');
    let text = '';
    let transcript = {
      person: '',
      image: '',
    };
    if (!textElement) {
      text = captionElement.textContent ?? '';
      transcript = this.getCaptionData(
        captionElement.parentElement as HTMLElement
      );
      if (!captionElement.id) {
        captionElement.id = `caption-${Date.now()}`;
      }
    } else {
      text = textElement.textContent ?? '';
      transcript = this.getCaptionData(captionElement);
      this.logger.log(`(ok) text: ${text} person: ${transcript.person}`);
      this.observeTextUpdate(textElement as HTMLElement);
      return;
    }

    const debounceEntry = this.captionsDebounceMap.get(captionElement);
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion

    if (!text.trim()) {
      this.logger.log(
        `text is empty for captionElement: ${captionElement.id} with caption: ${
          captionElement.textContent ?? 'no text found'
        }`
      );
      return;
    }

    if (debounceEntry) {
      clearTimeout(debounceEntry);
    }

    if (this.transcriptsCache.has(captionElement)) {
      this.handleCaptionUpdated({
        captionElement,
        person: transcript.person,
        text,
      });

      return;
    }

    this.logger.log('transcript: ' + text + ' Person:' + transcript.person);

    this.handleCaptionCreated({
      captionElement,
      image: transcript.image,
      person: transcript.person,
      text,
    });
  };

  private observeTextUpdate(captionElement: HTMLElement) {
    const observer = new MutationObserver((mutationRecords) => {
      mutationRecords.forEach((mutation) => {
        this.logger.log('Mutation observed ' + mutation.type);
        if (mutation.type === 'characterData') {
          if (
            mutation.target instanceof HTMLElement &&
            mutation.target.className.includes('nMcdL')
          ) {
            this.addCaption(mutation.target);
          } else {
            this.addCaption(mutation.target.parentElement as HTMLElement);
          }
        }
      });
    });
    const config = { characterData: true, subtree: true };
    observer.observe(captionElement, config);
  }

  private handleCaptionUpdated({
    captionElement,
    person,
    text,
  }: {
    captionElement: HTMLElement;
    person: string;
    text: string;
  }) {
    const currentValue = this.transcriptsCache.get(captionElement);

    if (!currentValue) {
      this.logger.error(
        new Error(`Caption element not found. Can't update caption.`)
      );

      return;
    }

    this.transcriptsCache.set(captionElement, {
      id: currentValue.id,
      transcript: { ...currentValue.transcript, endedAt: new Date(), text },
    });

    this.captionsDebounceMap.set(
      captionElement,
      setTimeout(() => {
        const cacheEntry = this.transcriptsCache.get(captionElement);

        if (!cacheEntry) {
          this.logger.error(
            new Error(`Caption element not found. Can't update caption.`)
          );

          return;
        }

        this.dispatchEvent(
          new CustomEvent<CaptionUpdatedEventDetail>(
            CAPTION_UPDATED_EVENT_NAME,
            {
              detail: {
                id: cacheEntry.id,
                ...cacheEntry.transcript,
              },
            }
          )
        );
      }, 1000)
    );

    this.handlePossibleTask(captionElement, person, text);
  }

  private handleCaptionCreated({
    captionElement,
    person,
    text,
    image,
  }: {
    captionElement: HTMLElement;
    person: string;
    text: string;
    image: string;
  }) {
    const now = new Date();
    const id = `${person}-${now.valueOf()}`;

    const transcript: Transcript = {
      image,
      person,
      text,
      startedAt: now,
      endedAt: now,
      language: this.language,
    };

    this.transcriptsCache.set(captionElement, { id, transcript });

    this.captionsDebounceMap.set(
      captionElement,
      setTimeout(() => {
        this.dispatchEvent(
          new CustomEvent<CaptionAddedEventDetail>(CAPTION_ADDED_EVENT_NAME, {
            detail: { id, ...transcript },
          })
        );

        this.transcriptsCache.set(captionElement, {
          id,
          transcript: transcript,
        });
      }, 1000)
    );

    this.handlePossibleTask(captionElement, person, text);
  }

  private handlePossibleTask(
    element: HTMLElement,
    person: string,
    captionText: string
  ) {
    if (this.tasksCache.has(person)) {
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      const tasksCacheEntry = this.tasksCache.get(person)!;

      clearTimeout(tasksCacheEntry.timeoutId as ReturnType<typeof setTimeout>);

      tasksCacheEntry.elements.add(element);
      tasksCacheEntry.timeoutId = setTimeout(
        () => this.persistTask(person),
        this.voiceCommandTimeout
      );

      return;
    }

    const voiceCommandMatcher = this.voiceCommandMatcher;

    if (!voiceCommandMatcher.isCommand(captionText)) {
      return;
    }

    this.tasksCache.set(person, {
      elements: new Set([element]),
      timeoutId: setTimeout(
        () => this.persistTask(person),
        this.voiceCommandTimeout
      ),
    });
  }

  private persistTask(person: string) {
    const tasksCacheEntry = this.tasksCache.get(person);

    if (!tasksCacheEntry) {
      return;
    }

    this.tasksCache.delete(person);

    const textContent = Array.from(tasksCacheEntry.elements).reduce(
      (result, element) => `${result}${element.textContent ?? ''}`,
      ''
    );
    const voiceCommandMatcher = this.voiceCommandMatcher;
    const text = voiceCommandMatcher.trimHotWord(textContent);

    if (voiceCommandMatcher.isRecord(text)) {
      const record = voiceCommandMatcher.trimRecordHotWord(text);

      this.dispatchEvent(
        new CustomEvent<RecordCreatedEventDetail>(RECORD_CREATED_EVENT_NAME, {
          detail: {
            title: record,
            'creator-name': person,
            language: this.language,
          },
        })
      );
    } else {
      this.dispatchEvent(
        new CustomEvent<TaskCreatedEventDetail>(TASK_CREATED_EVENT_NAME, {
          detail: {
            title: text,
            'creator-name': person,
            language: this.language,
          },
        })
      );
    }
    // this.chatService.sendChatMessage(`Task created by ${person}: ${text}`);
  }

  private getCaptionData(node: HTMLElement) {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const image = node.querySelector('img')!;
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const person = node.querySelector('div')!.textContent!;
    return {
      image: image.src,
      person,
    };
  }
}
