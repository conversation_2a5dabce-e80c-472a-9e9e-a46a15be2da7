const modifyConfig = /** @arg config {import('webpack').Configuration} @return {import('webpack').Configuration} */(config) => {
  return {
    ...config,
    entry: {
      content: ['./packages/plugin/src/app/content.ts'],
      background: ['./packages/plugin/src/app/background.ts'],
      options: ['./packages/plugin/src/app/options.ts'],
    },
    output: {
      path: config.output.path,
      filename: '[name].js',
    },
    target: undefined
  };
};

module.exports = modifyConfig
