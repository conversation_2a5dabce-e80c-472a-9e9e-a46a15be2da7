import 'reflect-metadata';
import { format } from 'date-fns';
import { ReflectiveInjector } from 'injection-js';

import { Logger, provideLoggerBackend } from '@kurt/shared';
import {
  createTranscriber,
  TRANSCRIPTION_STARTED_EVENT_NAME,
  NotificationEventDetail,
  NOTIFICATION_EVENT_NAME,
  TRANSCRIPTION_STOPPED_EVENT_NAME,
} from '@kurt/transcriber';

import {
  firebaseAppProvider,
  firebaseAudioStorageProvider,
  firebaseAuthProvider,
  firebaseFirestoreProvider,
} from './firebase-app';
import { FirebaseDatabaseBackend } from './firebase-database-backend';
import { loggerBackend } from './logger-backend';

const injector = ReflectiveInjector.resolveAndCreate([
  provideLoggerBackend(loggerBackend),
  Logger,
  firebaseAppProvider,
  firebaseAuthProvider,
  firebaseFirestoreProvider,
  firebaseAudioStorageProvider,
  FirebaseDatabaseBackend,
]);
const dateString = `${format(new Date(), 'yyyy-MM-dd')}`;
const pathString = new URL(window.location.href).pathname.replace('/', '');
const meetingId = `${pathString}-${dateString}`;
const databaseBackend = injector.get(
  FirebaseDatabaseBackend
) as FirebaseDatabaseBackend;

const transcriber = createTranscriber({
  meetingDocumentId: meetingId,
  databaseBackend,
  loggerBackend,
});
const commandHandlers: Readonly<
  Record<ExtensionCommand['commandType'], () => void>
> = {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  START_TRANSCRIPTION: transcriber.startTranscription,
  // eslint-disable-next-line @typescript-eslint/naming-convention
  STOP_TRANSCRIPTION: transcriber.stopTranscription,
  // eslint-disable-next-line @typescript-eslint/naming-convention
  TOGGLE_TRANSCRIPTION: transcriber.toggleTranscription,
} as const;

chrome.runtime.onMessage.addListener((message: ExtensionMessage | string) => {
  if (typeof message === 'string') {
    return false;
  }

  if (message.messageType === 'EVENT') {
    return false;
  }

  commandHandlers[message.commandType]?.();

  return true;
});

const dispatchEvent = (event: Omit<ExtensionEvent, 'messageType'>) => {
  chrome.runtime.sendMessage({ ...event, messageType: 'EVENT' });
};

transcriber.addEventListener(TRANSCRIPTION_STARTED_EVENT_NAME, () => {
  dispatchEvent({ eventType: 'TRANSCRIPTION_STARTED' });
});

transcriber.addEventListener(TRANSCRIPTION_STOPPED_EVENT_NAME, () => {
  dispatchEvent({ eventType: 'TRANSCRIPTION_STOPPED' });
});

transcriber.addEventListener(
  NOTIFICATION_EVENT_NAME,
  (event: CustomEvent<NotificationEventDetail>) => {
    dispatchEvent({ eventType: 'NOTIFICATION', ...event.detail });
  }
);
