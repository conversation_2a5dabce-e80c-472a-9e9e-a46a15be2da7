import { logoObjectUrl } from './logo';

const eventHandlers: Partial<
  Record<
    ExtensionEvent['eventType'],
    (event: ExtensionEvent, sender: chrome.runtime.MessageSender) => void
  >
> = {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  TRANSCRIPTION_STARTED: (_event, sender) => {
    chrome.action.setBadgeText({
      tabId: sender.tab?.id,
      text: 'ON',
    });
  },
  // eslint-disable-next-line @typescript-eslint/naming-convention
  TRANSCRIPTION_STOPPED: (_event, sender) => {
    chrome.action.setBadgeText({
      tabId: sender.tab?.id,
      text: 'OFF',
    });
  },
  // eslint-disable-next-line @typescript-eslint/naming-convention
  NOTIFICATION: (event) => {
    const { message, title } = event as NotificationEvent;
    chrome.notifications.create({
      type: 'basic',
      title,
      message,
      iconUrl: logoObjectUrl,
    });
  },
};

chrome.runtime.onInstalled.addListener(() => {
  chrome.action.disable();

  // Clear all rules to ensure only our expected rules are set
  chrome.declarativeContent.onPageChanged.removeRules(undefined, () => {
    // Declare a rule to enable the action on google.com pages
    const exampleRule = {
      conditions: [
        new chrome.declarativeContent.PageStateMatcher({
          pageUrl: { hostEquals: 'meet.google.com.rproxy.goskope.com' },
        }),
      ],

      actions: [new chrome.declarativeContent.ShowPageAction()],
    };

    // Finally, apply our new array of rules
    const rules = [exampleRule];
    chrome.declarativeContent.onPageChanged.addRules(rules);
  });
});

chrome.action.onClicked.addListener((tab) => {
  sendCommand(tab.id, { commandType: 'TOGGLE_TRANSCRIPTION' });
});

chrome.runtime.onMessage.addListener(
  (message: ExtensionMessage | string, sender) => {
    if (typeof message === 'string') {
      return;
    }

    if (message.messageType === 'COMMAND') {
      return;
    }

    eventHandlers[message.eventType]?.(message, sender);
  }
);

// function handleMainFrameNavigation(
//   event: chrome.webNavigation.WebNavigationTransitionCallbackDetails
// ) {
//   const url = new URL(event.url);

//   if (url.host.endsWith('meet.google.com')) {
//     return;
//   }

//   sendCommand(event.tabId, { commandType: 'STOP_TRANSCRIPTION' });
// }

// function handleChildFrameNavigation(
//   event: chrome.webNavigation.WebNavigationTransitionCallbackDetails
// ) {
//   chrome.webNavigation.getFrame(
//     {
//       frameId: event.frameId,
//       tabId: event.tabId,
//     },
//     (childFrameDetails) => {
//       if (!childFrameDetails) {
//         return;
//       }

//       chrome.webNavigation.getFrame(
//         {
//           frameId: childFrameDetails.parentFrameId,
//           tabId: event.tabId,
//         },
//         (parentFrameDetails) => {
//           if (!parentFrameDetails) {
//             return;
//           }

//           const tabUrl = new URL(parentFrameDetails.url);

//           if (!tabUrl.host.endsWith('meet.google.com')) {
//             return;
//           }

//           sendCommand(event.tabId, { commandType: 'START_TRANSCRIPTION' });
//         }
//       );
//     }
//   );
// }

function sendCommand(
  tabId: number,
  message: Omit<ExtensionCommand, 'messageType'>
) {
  chrome.tabs.sendMessage(tabId, { ...message, messageType: 'COMMAND' });
}
