import { UserImpl } from '@firebase/auth/internal';
import { FirebaseApp, initializeApp } from 'firebase/app';
import { Auth, getAuth } from 'firebase/auth';
import { Firestore, getFirestore } from 'firebase/firestore';
import { FirebaseStorage, getStorage } from 'firebase/storage';
import { InjectionToken, Provider } from 'injection-js';

export const FIREBASE_APP = new InjectionToken<FirebaseApp>('Firebase App');

export const firebaseAppProvider: Provider = {
  provide: FIREBASE_APP,
  useFactory: () => {
    return initializeApp({
      apiKey: 'AIzaSyBrPlfcbDdnEDR3Nx9vsTZTmAF2yS9oTMw',
      authDomain: 'd-nbi-kurt.firebaseapp.com',
      projectId: 'd-nbi-kurt',
      storageBucket: 'd-nbi-kurt.appspot.com',
      messagingSenderId: '546060646634',
      appId: '1:546060646634:web:94dc74d117f00a29a1ff93',
      measurementId: 'G-TYKDGGNN4X',
    });
  },
};

export const FIREBASE_AUTH = new InjectionToken<Auth>('Firebase Auth');

export const firebaseAuthProvider: Provider = {
  provide: FIREBASE_AUTH,
  useFactory: (app: FirebaseApp) => {
    const auth = getAuth(app);

    chrome.storage.local.get('user', ({ user }) => {
      if (user) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-argument
        auth.updateCurrentUser(UserImpl._fromJSON(auth as any, user));

        auth.onIdTokenChanged((e) => e.getIdToken());
      }
    });

    return auth;
  },
  deps: [FIREBASE_APP],
};

export const FIREBASE_FIRESTORE = new InjectionToken<Firestore>(
  'Firebase Firestore'
);

export const firebaseFirestoreProvider: Provider = {
  provide: FIREBASE_FIRESTORE,
  useFactory: (firebaseApp: FirebaseApp) => {
    return getFirestore(firebaseApp);
  },
  deps: [FIREBASE_APP],
};

export const FIREBASE_AUDIO_STORAGE = new InjectionToken<FirebaseStorage>(
  'Firebase Audio Storage'
);

export const firebaseAudioStorageProvider: Provider = {
  provide: FIREBASE_AUDIO_STORAGE,
  useFactory: (firebaseApp: FirebaseApp) => {
    return getStorage(firebaseApp, 'kurt-audio-recordings');
  },
};
