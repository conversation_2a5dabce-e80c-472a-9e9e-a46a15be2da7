import 'reflect-metadata';

import { UserImpl } from '@firebase/auth/internal';
import { Auth, EmailAuthProvider, User } from 'firebase/auth';
import * as firebaseui from 'firebaseui';
import { ReflectiveInjector } from 'injection-js';

import {
  firebaseAppProvider,
  firebaseAuthProvider,
  FIREBASE_AUTH,
} from './firebase-app';

const injector = ReflectiveInjector.resolveAndCreate([
  firebaseAppProvider,
  firebaseAuthProvider,
]);
const auth = injector.get(FIREBASE_AUTH) as Auth;

const ui = new firebaseui.auth.AuthUI(auth);
// eslint-disable-next-line @typescript-eslint/no-non-null-assertion
const root = document.querySelector('#root')!;

if (auth.currentUser) {
  root.textContent = `Logged in as ${auth.currentUser.displayName}`;
} else {
  chrome.storage.local.get('user', ({ user }) => {
    if (user) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-argument
      auth.updateCurrentUser(UserImpl._fromJSON(auth as any, user));
      showLogout(user as User);
    } else {
      showLoginForm();
    }
  });
}

function showLogout(user: User) {
  root.innerHTML = `<div style="display: flex; flex-direction: column">
      <div>Logged in as ${user.displayName}</div>
      <button id="logout" type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-button--colored" style="margin: 2rem auto">Logout</button>
    </div>
    `;
  requestAnimationFrame(() => {
    document.querySelector('#logout')?.addEventListener(
      'click',
      () => {
        chrome.storage.local.set({ user: null });
        auth.signOut();
        showLoginForm();
      },
      { once: true }
    );
  });
}

function showLoginForm() {
  root.innerHTML = '';

  ui.start(root, {
    signInOptions: [EmailAuthProvider.PROVIDER_ID],
    callbacks: {
      signInSuccessWithAuthResult: function (authResult: { user: User }) {
        const user = JSON.parse(JSON.stringify(authResult.user)) as User;
        // User successfully signed in.
        // Return type determines whether we continue the redirect automatically
        // or whether we leave that to developer to handle.
        chrome.storage.local.set({
          user,
        });

        showLogout(user);

        return false;
      },
    },
  });
}
