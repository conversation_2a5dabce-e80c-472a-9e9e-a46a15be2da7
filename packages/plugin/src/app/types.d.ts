declare interface ExtensionCommandBase {
  readonly messageType: 'COMMAND';
}

declare interface ToggleTranscriptionCommand extends ExtensionCommandBase {
  readonly commandType: 'TOGGLE_TRANSCRIPTION';
}

declare interface StartTranscriptionCommand extends ExtensionCommandBase {
  readonly commandType: 'START_TRANSCRIPTION';
}

declare interface StopTranscriptionCommand extends ExtensionCommandBase {
  readonly commandType: 'STOP_TRANSCRIPTION';
}

declare type ExtensionCommand =
  | ToggleTranscriptionCommand
  | StartTranscriptionCommand
  | StopTranscriptionCommand;

declare interface Transcript {
  person: string;
  image: string;
  text: string;
  startedAt: Date;
  endedAt: Date;
}

declare interface ExtensionEventBase {
  readonly messageType: 'EVENT';
}

declare interface TranscriptionStartedEvent extends ExtensionEventBase {
  readonly eventType: 'TRANSCRIPTION_STARTED';
}

declare interface TranscriptionStoppedEvent extends ExtensionEventBase {
  readonly eventType: 'TRANSCRIPTION_STOPPED';
}

declare interface NotificationEvent extends ExtensionEventBase {
  readonly eventType: 'NOTIFICATION';
  readonly title: string;
  readonly message: string;
}

declare type ExtensionEvent =
  | TranscriptionStartedEvent
  | TranscriptionStoppedEvent
  | NotificationEvent;

declare type ExtensionMessage = ExtensionCommand | ExtensionEvent;

declare interface Participant {
  avatar: string;
  name: string;
  title?: string;
  subtitle?: string;
}

declare interface Task {
  title: string;
  ['creator-name']: string;
}
