import { Auth } from 'firebase/auth';
import {
  collection,
  doc,
  DocumentReference,
  Firestore,
  getDoc,
  setDoc,
  updateDoc,
} from 'firebase/firestore';
import { FirebaseStorage, ref, uploadString } from 'firebase/storage';
import { Inject, Injectable } from 'injection-js';

import {
  DatabaseBackend,
  ExtensionEvent,
  Logger,
  Meeting,
  MeetingCollection,
  Screenshot,
  TaskOrRecord,
  Transcript,
} from '@kurt/shared';

import {
  FIREBASE_AUDIO_STORAGE,
  FIREBASE_AUTH,
  FIREBASE_FIRESTORE,
} from './firebase-app';

@Injectable()
export class FirebaseDatabaseBackend implements DatabaseBackend {
  private meetingDocumentRef!: DocumentReference;
  private meetingId!: string;

  constructor(
    @Inject(FIREBASE_AUTH) private auth: Auth,
    @Inject(FIREBASE_FIRESTORE) private db: Firestore,
    @Inject(FIREBASE_AUDIO_STORAGE) private audioStorage: FirebaseStorage,
    private logger: Logger
  ) {}

  initialize(meetingId: string): Promise<void> {
    this.meetingId = meetingId;
    this.meetingDocumentRef = doc(
      collection(this.db, 'meetings'),
      this.meetingId
    );

    return Promise.resolve();
  }

  getCurrentUserDisplayName(): Promise<string> {
    return Promise.resolve(
      this.auth.currentUser.displayName || this.auth.currentUser.email
    );
  }

  getCurrentUserEmail(): Promise<string> {
    return Promise.resolve(this.auth.currentUser.email);
  }

  async getMeeting(): Promise<Meeting | undefined> {
    const meeting = await getDoc(this.meetingDocumentRef);

    return meeting.data() as Meeting | undefined;
  }

  async createMeeting(meeting: Meeting): Promise<Meeting> {
    await setDoc(this.meetingDocumentRef, meeting);

    return this.getMeeting();
  }

  async updateMeeting(updates: Partial<Meeting>): Promise<Meeting> {
    await updateDoc(this.meetingDocumentRef, updates);

    return this.getMeeting();
  }

  async createDocument(
    collectionName: MeetingCollection,
    id: string,
    data: Transcript | TaskOrRecord | ExtensionEvent | Screenshot
  ): Promise<void> {
    await setDoc(
      doc(collection(this.meetingDocumentRef, collectionName), id),
      data
    );
  }

  updateDocument = async (
    collectionName: MeetingCollection,
    id: string,
    data: Partial<Transcript>
  ): Promise<void> => {
    await updateDoc(
      doc(collection(this.meetingDocumentRef, collectionName), id),
      data
    );
  };

  async saveAudioRecording({
    data,
    endedAt,
    startedAt,
  }: {
    data: string[];
    startedAt: Date;
    endedAt: Date;
  }): Promise<void> {
    const startTimestamp = startedAt.valueOf();
    const endTimestamp = endedAt.valueOf();
    const fileUploads = data.map((file, index) => {
      const filename = `${this.meetingId}-${startTimestamp}-${endTimestamp}-${index}.webm`;

      return uploadString(
        ref(this.audioStorage, filename),
        file,
        'base64'
      ).then(() =>
        setDoc(doc(collection(this.meetingDocumentRef, 'audioRecordings')), {
          filename,
          startedAt,
          endedAt,
        })
      );
    });

    try {
      await Promise.all(fileUploads);
    } catch (e) {
      this.logger.error(e as Error);
    }
  }
}
