{"folders": [{"name": "Root", "path": "."}, {"name": "bot", "path": "packages/bot"}, {"name": "chrome-extension", "path": "packages/chrome-extension"}, {"name": "plugin", "path": "packages/plugin"}, {"name": "shared", "path": "packages/shared"}, {"name": "transcriber", "path": "packages/transcriber"}], "settings": {"editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "editor.formatOnSave": true, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "terminal.integrated.cwd": "${workspaceFolder:Root}", "typescript.tsdk": "Root/node_modules/typescript/lib", "editor.bracketPairColorization.enabled": true, "javascript.preferences.importModuleSpecifier": "project-relative", "typescript.preferences.importModuleSpecifier": "project-relative"}}