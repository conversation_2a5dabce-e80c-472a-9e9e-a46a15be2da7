#!/bin/bash

# Simple build script for the bot package without using Nx
echo "Building bot package..."

# Check TypeScript compilation
echo "Checking TypeScript compilation..."
npx tsc --noEmit --project packages/bot/tsconfig.app.json
if [ $? -ne 0 ]; then
    echo "TypeScript compilation failed!"
    exit 1
fi

echo "TypeScript compilation successful!"

# Check shared package
echo "Checking shared package..."
npx tsc --noEmit --project packages/shared/tsconfig.lib.json
if [ $? -ne 0 ]; then
    echo "Shared package TypeScript compilation failed!"
    exit 1
fi

echo "Shared package compilation successful!"

# Check transcriber package
echo "Checking transcriber package..."
npx tsc --noEmit --project packages/transcriber/tsconfig.lib.json
if [ $? -ne 0 ]; then
    echo "Transcriber package TypeScript compilation failed!"
    exit 1
fi

echo "All packages compiled successfully!"
echo "Build completed successfully!"
