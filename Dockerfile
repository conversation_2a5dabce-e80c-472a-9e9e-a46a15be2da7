FROM node:lts
WORKDIR /usr/kurt/
COPY . .
RUN npm ci && npx nx build bot

FROM node:lts
WORKDIR /usr/kurt/
# Install latest chrome dev package and fonts to support major charsets (Chinese, Japanese, Arabic, Hebrew, Thai and a few others)
# Note: this installs the necessary libs to make the bundled version of Chromium that Puppeteer
# installs, work.
RUN apt-get update \
    && apt-get install -y wget gnupg \
    && wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list' \
    && apt-get update \
    && apt-get install -y google-chrome-stable fonts-ipafont-gothic fonts-wqy-zenhei fonts-thai-tlwg fonts-kacst fonts-freefont-ttf libxss1 pulseaudio pavucontrol \
    gconf-service libasound2 libatk1.0-0 libc6 libcairo2 libcups2 libdbus-1-3 \
libexpat1 libfontconfig1 libgcc1 libgconf-2-4 libgdk-pixbuf2.0-0 libglib2.0-0 libgtk-3-0 libnspr4 \
libpango-1.0-0 libpangocairo-1.0-0 libstdc++6 libx11-6 libx11-xcb1 libxcb1 libxcomposite1 \
libxcursor1 libxdamage1 libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libxss1 libxtst6 \
ca-certificates fonts-liberation libappindicator1 libnss3 lsb-release xdg-utils wget \
xvfb x11vnc x11-xkb-utils xfonts-100dpi xfonts-75dpi xfonts-scalable x11-apps \
ffmpeg \
      --no-install-recommends \
    && rm -rf /var/lib/apt/lists/*
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD true
RUN mkdir debug
COPY --from=0 /usr/kurt/package*.json ./
COPY --from=0 /usr/kurt/dist/packages/bot/main.js ./
COPY --from=0 /usr/kurt/dist/packages/bot/transcriber-script/main.js ./dist/packages/bot/transcriber-script/main.js
RUN npm ci --omit=dev
ENV NODE_ENV=production
ENV DISPLAY=:99
ENV PORT=8080

EXPOSE 8080

#CMD Xvfb :99 -screen 0 1024x768x16 & npx nx serve bot
CMD Xvfb :99 -screen 0 1024x768x16 & node main.js
